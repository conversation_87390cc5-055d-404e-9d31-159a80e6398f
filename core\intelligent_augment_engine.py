"""
Intelligent Augment Engine - Redesigned for complex tasks.

This system:
1. Plans the complete architecture before coding
2. Validates dependencies and structure
3. Generates complete, self-contained solutions
4. Uses intelligent error correction with full context understanding
"""

import ast
import re
import time
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from rich.progress import Progress, SpinnerColumn, TextColumn

@dataclass
class TaskPlan:
    """Complete plan for implementing a task."""
    task_description: str
    required_components: List[str]  # Classes, functions, etc.
    required_imports: List[str]     # External libraries needed
    implementation_order: List[str] # Order to implement components
    dependencies: Dict[str, List[str]]  # Component dependencies
    estimated_complexity: int      # 1-10 scale

@dataclass
class ComponentSpec:
    """Specification for a single component."""
    name: str
    type: str  # 'class', 'function', 'variable'
    signature: str
    dependencies: List[str]
    implementation: str = ""

@dataclass
class ValidationResult:
    """Result of code validation."""
    is_valid: bool
    missing_components: List[str]
    undefined_references: List[str]
    import_issues: List[str]
    syntax_errors: List[str]
    suggestions: List[str]

class IntelligentAugmentEngine:
    """Redesigned Augment Engine that actually works for complex tasks."""

    def __init__(self):
        self.console = Console()
        self.max_iterations = 5
        self.current_plan: Optional[TaskPlan] = None
        self.implemented_components: Set[str] = set()

    def augment_code_intelligent(self, prompt: str, codebase_path: Path) -> Dict[str, Any]:
        """
        Main method: Intelligently implement complex tasks.

        Process:
        1. Analyze and plan the complete task
        2. Validate the plan
        3. Generate complete implementation
        4. Validate before execution
        5. Execute and fix issues intelligently
        """
        self.console.print(Panel(
            f"🧠 **INTELLIGENT AUGMENT ENGINE**\n\n"
            f"Task: {prompt}\n"
            f"Working Directory: {codebase_path}\n\n"
            f"Process:\n"
            f"1. 📋 Analyze and create complete implementation plan\n"
            f"2. 🔍 Validate plan and dependencies\n"
            f"3. 🎨 Generate complete, self-contained code\n"
            f"4. ✅ Pre-validate code structure\n"
            f"5. ⚡ Execute with intelligent error handling",
            title="🚀 Intelligent Augment Engine",
            border_style="bold blue"
        ))

        start_time = time.time()

        try:
            # Step 1: Create comprehensive plan
            self.console.print("📋 Creating comprehensive implementation plan...")
            plan = self._create_task_plan(prompt)
            self.current_plan = plan

            # Display the detailed plan
            self._display_plan(plan)

            # Step 2: Validate plan feasibility
            self.console.print("🔍 Validating plan feasibility...")
            plan_validation = self._validate_plan(plan)
            if not plan_validation['feasible']:
                return self._create_failure_result(f"Plan validation failed: {plan_validation['issues']}")

            # Display validation results
            self._display_validation_results(plan_validation)

            # Step 3: Generate complete implementation
            self.console.print("🎨 Generating complete implementation...")
            complete_code = self._generate_complete_implementation(plan)

            # Step 4: Pre-validate code structure
            self.console.print("✅ Pre-validating code structure...")
            validation = self._validate_code_structure(complete_code)

            if not validation.is_valid:
                self.console.print("🔧 Fixing structural issues...")
                complete_code = self._fix_structural_issues(complete_code, validation)

            # Step 5: Execute with intelligent error handling
            self.console.print("⚡ Executing with intelligent monitoring...")
            execution_result = self._execute_with_intelligent_handling(complete_code, codebase_path)

            end_time = time.time()

            return {
                'success': execution_result['success'],
                'final_code': execution_result['final_code'],
                'execution_time': end_time - start_time,
                'plan': plan,
                'iterations': execution_result['iterations'],
                'output': execution_result['output']
            }

        except Exception as e:
            return self._create_failure_result(f"Engine error: {str(e)}")

    def _display_plan(self, plan: TaskPlan):
        """Display the detailed implementation plan."""
        from rich.table import Table

        self.console.print(Panel(
            f"📋 **IMPLEMENTATION PLAN**\n\n"
            f"🎯 **Task**: {plan.task_description}\n"
            f"🔢 **Estimated Complexity**: {plan.estimated_complexity}/10\n"
            f"📦 **Components**: {len(plan.required_components)}\n"
            f"📚 **Imports**: {len(plan.required_imports)}",
            title="🧠 Task Analysis",
            border_style="bold cyan"
        ))

        # Components table
        if plan.required_components:
            components_table = Table(title="📦 Required Components")
            components_table.add_column("Order", style="cyan", no_wrap=True)
            components_table.add_column("Component", style="green")
            components_table.add_column("Dependencies", style="yellow")

            for i, component in enumerate(plan.implementation_order, 1):
                deps = plan.dependencies.get(component, [])
                deps_str = ", ".join(deps) if deps else "None"
                components_table.add_row(str(i), component, deps_str)

            self.console.print(components_table)

        # Imports table
        if plan.required_imports:
            imports_table = Table(title="📚 Required Imports")
            imports_table.add_column("Import", style="blue")

            for imp in plan.required_imports:
                imports_table.add_row(imp)

            self.console.print(imports_table)

    def _display_validation_results(self, validation: Dict[str, Any]):
        """Display plan validation results."""
        if validation['feasible']:
            self.console.print("[bold green]✅ Plan is feasible and ready for implementation![/bold green]")
        else:
            self.console.print("[bold yellow]⚠️ Plan has some issues but proceeding...[/bold yellow]")

        if validation['issues']:
            self.console.print("\n[bold yellow]Issues identified:[/bold yellow]")
            for issue in validation['issues']:
                self.console.print(f"  • {issue}")

        if validation['recommendations']:
            self.console.print("\n[bold blue]Recommendations:[/bold blue]")
            for rec in validation['recommendations']:
                self.console.print(f"  • {rec}")

        self.console.print()  # Add spacing

    def _create_task_plan(self, prompt: str) -> TaskPlan:
        """Create a comprehensive plan for the task."""
        # This would use LLM to analyze the task and create a detailed plan
        # For now, let's create a smart analysis based on the prompt

        required_components = []
        required_imports = []
        dependencies = {}

        # Analyze prompt for complexity indicators
        prompt_lower = prompt.lower()

        # Neural network task detection
        if any(term in prompt_lower for term in ['neural network', 'deep learning', 'tensorflow', 'pytorch']):
            required_components = [
                'create_dataset', 'NeuralNetwork', 'train_model', 'evaluate_model', 'show_progress'
            ]
            required_imports = ['numpy', 'matplotlib']  # Start with standard library alternatives
            dependencies = {
                'train_model': ['NeuralNetwork', 'create_dataset'],
                'evaluate_model': ['NeuralNetwork'],
                'show_progress': []
            }

        # Web scraping task detection
        elif any(term in prompt_lower for term in ['web scraping', 'scrape', 'requests', 'beautifulsoup']):
            required_components = [
                'WebScraper', 'fetch_url', 'parse_content', 'save_data'
            ]
            required_imports = ['urllib.request', 'urllib.parse']  # Use standard library
            dependencies = {
                'parse_content': ['fetch_url'],
                'save_data': ['parse_content']
            }

        # Data analysis task detection
        elif any(term in prompt_lower for term in ['data analysis', 'pandas', 'dataframe', 'csv']):
            required_components = [
                'DataAnalyzer', 'load_data', 'process_data', 'visualize_results'
            ]
            required_imports = ['csv', 'json']  # Use standard library
            dependencies = {
                'process_data': ['load_data'],
                'visualize_results': ['process_data']
            }

        # Game development task detection
        elif any(term in prompt_lower for term in ['game', 'pygame', 'graphics']):
            required_components = [
                'Game', 'GameLoop', 'render', 'update', 'handle_input'
            ]
            required_imports = []  # Use standard library only
            dependencies = {
                'GameLoop': ['Game'],
                'render': ['Game'],
                'update': ['Game']
            }

        # Calculator task detection
        elif any(term in prompt_lower for term in ['calculator', 'math', 'arithmetic', 'operations']):
            required_components = [
                'Calculator', 'add', 'subtract', 'multiply', 'divide', 'main'
            ]
            required_imports = []
            dependencies = {
                'main': ['Calculator', 'add', 'subtract', 'multiply', 'divide']
            }

        # Default: Simple task
        else:
            # Intelligent component extraction based on task type
            if any(term in prompt_lower for term in ['class', 'object']):
                # Object-oriented task
                task_name = prompt.split()[-1] if prompt.split() else 'Task'
                class_name = task_name.capitalize()
                required_components = [class_name, 'main']
                dependencies = {'main': [class_name]}
            elif any(term in prompt_lower for term in ['function', 'algorithm']):
                # Function-based task
                required_components = ['process_data', 'main']
                dependencies = {'main': ['process_data']}
            else:
                # Generic task - extract meaningful words
                words = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', prompt)
                meaningful_words = [word for word in words if len(word) > 3 and word.lower() not in ['create', 'simple', 'with', 'that', 'this', 'make', 'build']]
                required_components = meaningful_words[:3] + ['main']
                dependencies = {'main': meaningful_words[:3]}

            required_imports = []

        return TaskPlan(
            task_description=prompt,
            required_components=required_components,
            required_imports=required_imports,
            implementation_order=self._determine_implementation_order(required_components, dependencies),
            dependencies=dependencies,
            estimated_complexity=self._estimate_complexity(prompt)
        )

    def _determine_implementation_order(self, components: List[str], dependencies: Dict[str, List[str]]) -> List[str]:
        """Determine the correct order to implement components based on dependencies."""
        ordered = []
        remaining = set(components)

        while remaining:
            # Find components with no unmet dependencies
            ready = []
            for comp in remaining:
                deps = dependencies.get(comp, [])
                if all(dep in ordered or dep not in components for dep in deps):
                    ready.append(comp)

            if not ready:
                # Break circular dependencies by picking the first one
                ready = [list(remaining)[0]]

            ordered.extend(ready)
            remaining -= set(ready)

        return ordered

    def _estimate_complexity(self, prompt: str) -> int:
        """Estimate task complexity on a 1-10 scale."""
        complexity_indicators = {
            'neural network': 8,
            'deep learning': 9,
            'machine learning': 7,
            'web scraping': 6,
            'data analysis': 6,
            'game': 7,
            'api': 5,
            'database': 6,
            'gui': 7,
            'algorithm': 5
        }

        prompt_lower = prompt.lower()
        max_complexity = 3  # Base complexity

        for indicator, complexity in complexity_indicators.items():
            if indicator in prompt_lower:
                max_complexity = max(max_complexity, complexity)

        # Adjust for length and detail
        if len(prompt) > 100:
            max_complexity += 1
        if 'progress bar' in prompt_lower:
            max_complexity += 1
        if 'visualization' in prompt_lower:
            max_complexity += 1

        return min(max_complexity, 10)

    def _validate_plan(self, plan: TaskPlan) -> Dict[str, Any]:
        """Validate if the plan is feasible."""
        issues = []

        # Check if complexity is manageable
        if plan.estimated_complexity > 8:
            issues.append(f"High complexity ({plan.estimated_complexity}/10) - may need simplification")

        # Check for circular dependencies
        for comp, deps in plan.dependencies.items():
            if comp in deps:
                issues.append(f"Circular dependency detected: {comp} depends on itself")

        # Check if required imports are reasonable
        problematic_imports = ['tensorflow', 'torch', 'pygame', 'opencv-python']
        for imp in plan.required_imports:
            if imp in problematic_imports:
                issues.append(f"Complex import detected: {imp} - will use standard library alternative")

        return {
            'feasible': len(issues) == 0 or plan.estimated_complexity <= 6,
            'issues': issues,
            'recommendations': self._get_plan_recommendations(plan)
        }

    def _get_plan_recommendations(self, plan: TaskPlan) -> List[str]:
        """Get recommendations for improving the plan."""
        recommendations = []

        if plan.estimated_complexity > 7:
            recommendations.append("Consider breaking into smaller subtasks")

        if len(plan.required_components) > 10:
            recommendations.append("Large number of components - ensure clear interfaces")

        if not plan.dependencies:
            recommendations.append("No dependencies detected - verify component isolation")

        return recommendations

    def _generate_complete_implementation(self, plan: TaskPlan) -> str:
        """Generate complete, self-contained implementation using LLM."""
        from core.llm_integration import LLMManager

        llm_manager = LLMManager()

        # Create a comprehensive prompt for the LLM
        prompt = f"""Generate COMPLETE, WORKING Python code for: {plan.task_description}

CRITICAL REQUIREMENTS:
- Write COMPLETE, SELF-CONTAINED Python code that runs immediately
- Give only the code since your text response will be executed directly
- NO placeholders, undefined functions, or missing imports
- Use ONLY Python standard library (math, random, csv, json, etc.)
- NO external libraries like tensorflow, sklearn, pandas, etc.
- DEFINE ALL functions and classes that are used
- Include ALL data generation, processing, and output in one file
- The code must execute successfully from start to finish
- If you need data, GENERATE it in the code
- Include proper error handling and docstrings
- Add a main section that demonstrates the functionality
- NO interactive input() calls - use predefined test data instead
- NO infinite loops or user interaction - make it fully automated
- Include print statements to show the results

Required components to implement:
{', '.join(plan.required_components)}

Implementation order:
{', '.join(plan.implementation_order)}

Generate ONLY the complete Python code that runs without errors:"""

        # Generate the complete code using LLM
        generated_code = llm_manager.generate_code(prompt)

        return generated_code

    def _generate_component(self, component_name: str, plan: TaskPlan) -> str:
        """Generate code for a specific component using LLM."""
        from core.llm_integration import LLMManager

        llm_manager = LLMManager()

        prompt = f"""Generate COMPLETE Python code for the component: {component_name}

Task context: {plan.task_description}

CRITICAL REQUIREMENTS:
- Write COMPLETE, WORKING code for this specific component
- Use ONLY Python standard library (math, random, csv, json, etc.)
- NO external libraries like tensorflow, sklearn, pandas, etc.
- DEFINE ALL functions and classes completely
- Include proper error handling and docstrings
- Make it self-contained and functional

Generate ONLY the Python code for this component:"""

        return llm_manager.generate_code(prompt)

    def _generate_main_function(self, plan: TaskPlan) -> str:
        """Generate the main execution function using LLM."""
        from core.llm_integration import LLMManager

        llm_manager = LLMManager()

        prompt = f"""Generate COMPLETE Python main function for: {plan.task_description}

CRITICAL REQUIREMENTS:
- Write a complete main() function and if __name__ == "__main__": block
- Use ONLY the components that will be defined: {', '.join(plan.required_components)}
- Use ONLY Python standard library
- Include proper demonstration of all functionality
- Add print statements to show progress and results
- Make it self-contained and executable

Generate ONLY the main function code:"""

        return llm_manager.generate_code(prompt)

    def _generate_web_scraper_component(self, component_name: str) -> str:
        """Generate web scraper components using LLM."""
        from core.llm_integration import LLMManager

        llm_manager = LLMManager()

        prompt = f"""Generate COMPLETE Python code for web scraper component: {component_name}

CRITICAL REQUIREMENTS:
- Use ONLY Python standard library (urllib, re, json, etc.)
- NO external libraries like requests, beautifulsoup, etc.
- Write complete, working code for web scraping
- Include proper error handling
- Make it self-contained and functional

Generate ONLY the Python code for this web scraper component:"""

        return llm_manager.generate_code(prompt)

    def _generate_data_analysis_component(self, component_name: str) -> str:
        """Generate data analysis components using LLM."""
        from core.llm_integration import LLMManager

        llm_manager = LLMManager()

        prompt = f"""Generate COMPLETE Python code for data analysis component: {component_name}

CRITICAL REQUIREMENTS:
- Use ONLY Python standard library (csv, json, math, statistics, etc.)
- NO external libraries like pandas, numpy, matplotlib, etc.
- Write complete, working code for data analysis
- Include proper error handling and data processing
- Make it self-contained and functional

Generate ONLY the Python code for this data analysis component:"""

        return llm_manager.generate_code(prompt)

    def _generate_game_component(self, component_name: str) -> str:
        """Generate game components using LLM."""
        from core.llm_integration import LLMManager

        llm_manager = LLMManager()

        prompt = f"""Generate COMPLETE Python code for game component: {component_name}

CRITICAL REQUIREMENTS:
- Write complete, working code for a simple console game
- Include game logic, rendering, and input handling
- Make it self-contained and functional
- Include proper error handling

Generate ONLY the Python code for this game component:"""

        return llm_manager.generate_code(prompt)

    def _generate_generic_component(self, component_name: str) -> str:
        """Generate a generic component using LLM."""
        from core.llm_integration import LLMManager

        llm_manager = LLMManager()

        prompt = f"""Generate COMPLETE Python code for component: {component_name}

CRITICAL REQUIREMENTS:
- Use ONLY Python standard library
- Write complete, working code for this component
- Include proper error handling and docstrings
- Make it self-contained and functional
- If it's a class (starts with capital), include __init__ and main methods
- If it's a function, make it complete and working

Generate ONLY the Python code for this component:"""

        return llm_manager.generate_code(prompt)

    def _validate_code_structure(self, code: str) -> ValidationResult:
        """Validate code structure before execution."""
        missing_components = []
        undefined_references = []
        import_issues = []
        syntax_errors = []
        suggestions = []

        try:
            # Parse the code to check syntax
            tree = ast.parse(code)

            # Find all defined functions and classes
            defined_functions = set()
            defined_classes = set()

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    defined_functions.add(node.name)
                elif isinstance(node, ast.ClassDef):
                    defined_classes.add(node.name)

            # Find all function calls and class instantiations
            called_functions = set()
            used_classes = set()

            for node in ast.walk(tree):
                if isinstance(node, ast.Call):
                    if isinstance(node.func, ast.Name):
                        called_functions.add(node.func.id)
                    elif isinstance(node.func, ast.Attribute):
                        # Method calls - check if the object exists
                        if isinstance(node.func.value, ast.Name):
                            # This is a method call, we'll check the class later
                            pass
                elif isinstance(node, ast.Name) and isinstance(node.ctx, ast.Load):
                    # Variable/class usage
                    if node.id[0].isupper():  # Likely a class
                        used_classes.add(node.id)

            # Check for undefined functions
            for func in called_functions:
                if func not in defined_functions and func not in ['print', 'len', 'range', 'sum', 'max', 'min', 'abs', 'int', 'float', 'str', 'list', 'dict', 'set']:
                    undefined_references.append(f"Function '{func}' is called but not defined")

            # Check for undefined classes
            for cls in used_classes:
                if cls not in defined_classes:
                    undefined_references.append(f"Class '{cls}' is used but not defined")

        except SyntaxError as e:
            syntax_errors.append(f"Syntax error: {e}")

        # Check imports
        import_lines = [line.strip() for line in code.split('\n') if line.strip().startswith('import')]
        for line in import_lines:
            if any(problematic in line for problematic in ['tensorflow', 'torch', 'pygame', 'opencv']):
                import_issues.append(f"Problematic import detected: {line}")

        # Generate suggestions
        if undefined_references:
            suggestions.append("Consider implementing missing functions/classes or using standard library alternatives")
        if import_issues:
            suggestions.append("Replace complex imports with standard library equivalents")

        is_valid = not (missing_components or undefined_references or import_issues or syntax_errors)

        return ValidationResult(
            is_valid=is_valid,
            missing_components=missing_components,
            undefined_references=undefined_references,
            import_issues=import_issues,
            syntax_errors=syntax_errors,
            suggestions=suggestions
        )

    def _fix_structural_issues(self, code: str, validation: ValidationResult) -> str:
        """Fix structural issues in the code."""
        fixed_code = code

        # Fix import issues
        for issue in validation.import_issues:
            if 'tensorflow' in issue:
                fixed_code = fixed_code.replace('import tensorflow as tf', '# import tensorflow as tf  # Using standard library instead')
            elif 'torch' in issue:
                fixed_code = fixed_code.replace('import torch', '# import torch  # Using standard library instead')
            elif 'pygame' in issue:
                fixed_code = fixed_code.replace('import pygame', '# import pygame  # Using standard library instead')

        # Add missing simple functions
        for ref in validation.undefined_references:
            if "Function 'show_progress'" in ref:
                progress_func = '''
def show_progress(current, total, message="Progress"):
    """Simple progress indicator."""
    percent = (current / total) * 100
    print(f"{message}: {percent:.1f}% ({current}/{total})")
'''
                fixed_code = progress_func + "\n" + fixed_code

        return fixed_code

    def _execute_with_intelligent_handling(self, code: str, codebase_path: Path) -> Dict[str, Any]:
        """Execute code with intelligent error handling."""
        iterations = 0
        max_iterations = 3
        current_code = code

        while iterations < max_iterations:
            iterations += 1

            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
                f.write(current_code)
                temp_file = f.name

            try:
                # Execute the code
                result = subprocess.run(
                    ['python', temp_file],
                    cwd=str(codebase_path),
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                if result.returncode == 0:
                    # Success!
                    return {
                        'success': True,
                        'final_code': current_code,
                        'iterations': iterations,
                        'output': result.stdout
                    }
                else:
                    # Error occurred
                    error_output = result.stderr
                    self.console.print(f"[yellow]Iteration {iterations} failed: {error_output[:200]}...[/yellow]")

                    if iterations < max_iterations:
                        # Try to fix the error
                        current_code = self._intelligent_error_fix(current_code, error_output)

            except subprocess.TimeoutExpired:
                self.console.print(f"[yellow]Iteration {iterations} timed out[/yellow]")
                if iterations < max_iterations:
                    # Add timeout handling
                    current_code = self._add_timeout_handling(current_code)

            finally:
                # Clean up temp file
                try:
                    Path(temp_file).unlink()
                except:
                    pass

        return {
            'success': False,
            'final_code': current_code,
            'iterations': iterations,
            'output': 'Failed after maximum iterations'
        }

    def _intelligent_error_fix(self, code: str, error_output: str) -> str:
        """Apply intelligent fixes based on error output."""
        if "NameError" in error_output and "is not defined" in error_output:
            # Extract the undefined name
            import re
            match = re.search(r"name '(\w+)' is not defined", error_output)
            if match:
                undefined_name = match.group(1)

                # Add a simple implementation
                if undefined_name == 'show_progress':
                    fix = '''
def show_progress(current, total, message="Progress"):
    percent = (current / total) * 100
    print(f"{message}: {percent:.1f}%")

'''
                    return fix + code
                elif undefined_name.endswith('_data') or undefined_name.startswith('load_'):
                    # Use string concatenation to avoid syntax issues
                    fix = f"\ndef {undefined_name}():\n    \"\"\"Auto-generated function.\"\"\"\n    return []\n\n"
                    return fix + code

        elif "ModuleNotFoundError" in error_output:
            # Replace problematic imports
            if 'tensorflow' in error_output:
                code = code.replace('import tensorflow as tf', '# tensorflow not available')
            elif 'torch' in error_output:
                code = code.replace('import torch', '# torch not available')

        return code

    def _add_timeout_handling(self, code: str) -> str:
        """Add timeout handling to prevent infinite loops."""
        # Add a simple timeout mechanism that works on Windows
        timeout_code = '''
import sys
import time

# Simple timeout mechanism for Windows compatibility
start_time = time.time()
MAX_EXECUTION_TIME = 25  # 25 seconds

def check_timeout():
    if time.time() - start_time > MAX_EXECUTION_TIME:
        print("Execution timed out!")
        sys.exit(1)

'''
        return timeout_code + code

    def _create_failure_result(self, error_message: str) -> Dict[str, Any]:
        """Create a failure result."""
        return {
            'success': False,
            'final_code': '',
            'execution_time': 0,
            'plan': None,
            'iterations': 0,
            'output': f'Error: {error_message}'
        }