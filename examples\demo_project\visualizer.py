import csv
import matplotlib.pyplot as plt
import seaborn as sns

class DataAnalyzer:
    def __init__(self, data):
        self.data = data

    def calculate_mean(self):
        return sum(self.data) / len(self.data)

    def calculate_median(self):
        sorted_data = sorted(self.data)
        n = len(sorted_data)
        if n % 2 == 1:
            return sorted_data[n // 2]
        else:
            return (sorted_data[n // 2 - 1] + sorted_data[n // 2]) / 2

    def calculate_std_dev(self):
        mean = self.calculate_mean()
        variance = sum((x - mean) ** 2 for x in self.data) / len(self.data)
        return variance ** 0.5

class Visualizer:
    def __init__(self, data_analyzer):
        self.data_analyzer = data_analyzer

    def plot_results(self):
        plt.figure(figsize=(10, 6))
        sns.histplot(self.data_analyzer.data, kde=True)
        plt.title('Data Distribution')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
        plt.show()

def process_data(data):
    return [float(x) for x in data]

if __name__ == "__main__":
    # Generate some random data
    data = generate_random_data(100)
    
    # Process the data
    processed_data = process_data(data)
    
    # Create a DataAnalyzer object
    analyzer = DataAnalyzer(processed_data)
    
    # Create a Visualizer object
    visualizer = Visualizer(analyzer)
    
    # Plot the results
    visualizer.plot_results()