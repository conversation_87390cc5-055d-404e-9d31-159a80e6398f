# Calculator Class
class Calculator:
    """A simple calculator class with add, subtract, multiply, and divide methods."""

    def __init__(self):
        pass

    def add(self, a, b):
        """Adds two numbers together.

        Args:
            a (int or float): The first number to add.
            b (int or float): The second number to add.

        Returns:
            int or float: The sum of the two numbers.
        """
        return a + b

    def subtract(self, a, b):
        """Subtracts one number from another.

        Args:
            a (int or float): The number to be subtracted from.
            b (int or float): The number to subtract.

        Returns:
            int or float: The difference between the two numbers.
        """
        return a - b

    def multiply(self, a, b):
        """Multiplies two numbers together.

        Args:
            a (int or float): The first number to multiply.
            b (int or float): The second number to multiply.

        Returns:
            int or float: The product of the two numbers.
        """
        return a * b

    def divide(self, a, b):
        """Divides one number by another.

        Args:
            a (int or float): The number to be divided.
            b (int or float): The number to divide by.

        Returns:
            int or float: The quotient of the two numbers.
        """
        return a / b

# Main Section
if __name__ == "__main__":
    # Testing the calculator class
    calc = Calculator()
    print("Addition:", calc.add(3, 5))
    print("Subtraction:", calc.subtract(10, 4))
    print("Multiplication:", calc.multiply(2, 6))
    print("Division:", calc.divide(12, 4))