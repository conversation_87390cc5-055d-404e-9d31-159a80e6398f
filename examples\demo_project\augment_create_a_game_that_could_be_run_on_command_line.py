import random

class Player:
    def __init__(self, symbol):
        self.symbol = symbol

    def get_move(self, board):
        valid_moves = [i for i in range(9) if board.board[i] == ' ']
        return random.choice(valid_moves)

class Board:
    def __init__(self):
        self.board = [' '] * 9
        self.winning_combinations = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8],
            [0, 3, 6], [1, 4, 7], [2, 5, 8],
            [0, 4, 8], [2, 4, 6]
        ]

    def display(self):
        print(f"{self.board[0]} | {self.board[1]} | {self.board[2]}")
        print("---------")
        print(f"{self.board[3]} | {self.board[4]} | {self.board[5]}")
        print("---------")
        print(f"{self.board[6]} | {self.board[7]} | {self.board[8]}")

    def make_move(self, player, position):
        if self.board[position] == ' ':
            self.board[position] = player.symbol
            return True
        return False

    def check_winner(self, player):
        for combo in self.winning_combinations:
            if all(self.board[i] == player.symbol for i in combo):
                return True
        return False

def main():
    board = Board()
    players = [Player('X'), Player('O')]
    current_player_index = 0

    while True:
        board.display()
        move = players[current_player_index].get_move(board)
        if board.make_move(players[current_player_index], move):
            if board.check_winner(players[current_player_index]):
                board.display()
                print(f"Player {players[current_player_index].symbol} wins!")
                break
            current_player_index = 1 - current_player_index
        else:
            print("Invalid move. Try again.")

if __name__ == "__main__":
    main()