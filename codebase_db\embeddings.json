{"ecebed851493d783": {"name": "validate_number", "type": "function", "parent": null, "start_line": 5, "end_line": 11, "complexity": 2, "has_docstring": true, "content_length": 164, "token_count": 48, "document": "function validate_number"}, "f61d7d3d7d8cbafa": {"name": "format_result", "type": "function", "parent": null, "start_line": 13, "end_line": 17, "complexity": 2, "has_docstring": true, "content_length": 154, "token_count": 47, "document": "function format_result"}, "4e83f777cc50c47b": {"name": "validate_number", "type": "function", "parent": null, "start_line": 6, "end_line": 12, "complexity": 2, "has_docstring": true, "content_length": 164, "token_count": 48, "document": "function validate_number"}, "98ded7fa6618575a": {"name": "format_result", "type": "function", "parent": null, "start_line": 14, "end_line": 18, "complexity": 2, "has_docstring": true, "content_length": 154, "token_count": 47, "document": "function format_result"}, "da2e83ee5e32dd98": {"name": "add", "type": "function", "parent": null, "start_line": 5, "end_line": 7, "complexity": 1, "has_docstring": true, "content_length": 58, "token_count": 27, "document": "function add"}, "5194b77fc11af7b6": {"name": "subtract", "type": "function", "parent": null, "start_line": 9, "end_line": 11, "complexity": 1, "has_docstring": true, "content_length": 65, "token_count": 30, "document": "function subtract"}, "aef9eb87faf68850": {"name": "multiply", "type": "function", "parent": null, "start_line": 13, "end_line": 15, "complexity": 1, "has_docstring": true, "content_length": 68, "token_count": 27, "document": "function multiply"}, "ce11f314f88d2767": {"name": "divide", "type": "function", "parent": null, "start_line": 17, "end_line": 25, "complexity": 2, "has_docstring": true, "content_length": 196, "token_count": 59, "document": "function divide"}, "8e7b611a1780ef62": {"name": "modulo", "type": "function", "parent": null, "start_line": 27, "end_line": 35, "complexity": 2, "has_docstring": true, "content_length": 222, "token_count": 66, "document": "function modulo"}, "0266cca3db300d96": {"name": "power", "type": "function", "parent": null, "start_line": 37, "end_line": 45, "complexity": 2, "has_docstring": true, "content_length": 193, "token_count": 60, "document": "function power"}, "43914818f64a051d": {"name": "main", "type": "function", "parent": null, "start_line": 47, "end_line": 85, "complexity": 11, "has_docstring": true, "content_length": 1291, "token_count": 365, "document": "function main"}, "6c8374568999a4c3": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "9195eee0af03934d": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "function", "parent": null, "start_line": 3, "end_line": 16, "complexity": 2, "has_docstring": true, "content_length": 307, "token_count": 110, "document": "function <PERSON><PERSON><PERSON><PERSON>"}, "e7040a608e27f1cc": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "4f1a80d0821b1d50": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 14, "token_count": 6, "document": "import imports"}, "9d50a6f6fceb5d9d": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "fe04d483ff6a3707": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 31, "token_count": 10, "document": "import imports"}, "6b7c630a6e72e5ff": {"name": "FileScanner", "type": "class", "parent": null, "start_line": 6, "end_line": 7, "complexity": 0, "has_docstring": true, "content_length": 87, "token_count": 35, "document": "class FileScanner"}, "6fe6d99bfa9cd6d5": {"name": "__init__", "type": "function", "parent": null, "start_line": 9, "end_line": 10, "complexity": 1, "has_docstring": false, "content_length": 69, "token_count": 20, "document": "function __init__"}, "a4f960cfe61527a2": {"name": "scan", "type": "function", "parent": null, "start_line": 12, "end_line": 18, "complexity": 3, "has_docstring": false, "content_length": 253, "token_count": 55, "document": "function scan"}, "d9990a997205a855": {"name": "FileAnalyzer", "type": "class", "parent": null, "start_line": 20, "end_line": 21, "complexity": 0, "has_docstring": true, "content_length": 66, "token_count": 31, "document": "class FileAnalyzer"}, "2eda29a03e11be4b": {"name": "__init__", "type": "function", "parent": null, "start_line": 23, "end_line": 24, "complexity": 1, "has_docstring": false, "content_length": 69, "token_count": 23, "document": "function __init__"}, "c13833ac32e92c63": {"name": "analyze", "type": "function", "parent": null, "start_line": 26, "end_line": 33, "complexity": 3, "has_docstring": false, "content_length": 344, "token_count": 81, "document": "function analyze"}, "1f6cad0d52acb020": {"name": "DuplicateFinder", "type": "class", "parent": null, "start_line": 35, "end_line": 36, "complexity": 0, "has_docstring": true, "content_length": 69, "token_count": 26, "document": "class DuplicateFinder"}, "3e34ce8dd0bb9af7": {"name": "__init__", "type": "function", "parent": null, "start_line": 38, "end_line": 39, "complexity": 1, "has_docstring": false, "content_length": 69, "token_count": 23, "document": "function __init__"}, "5608b1a4dac63899": {"name": "find", "type": "function", "parent": null, "start_line": 41, "end_line": 50, "complexity": 4, "has_docstring": false, "content_length": 377, "token_count": 79, "document": "function find"}, "6b1090700be700c4": {"name": "ReportGenerator", "type": "class", "parent": null, "start_line": 52, "end_line": 53, "complexity": 0, "has_docstring": true, "content_length": 58, "token_count": 21, "document": "class ReportGenerator"}, "eb860faa4d201cd5": {"name": "__init__", "type": "function", "parent": null, "start_line": 55, "end_line": 56, "complexity": 1, "has_docstring": false, "content_length": 69, "token_count": 23, "document": "function __init__"}, "c8e12512785140b2": {"name": "generate", "type": "function", "parent": null, "start_line": 58, "end_line": 65, "complexity": 3, "has_docstring": false, "content_length": 358, "token_count": 85, "document": "function generate"}, "f0fa090775156e0a": {"name": "main", "type": "function", "parent": null, "start_line": 67, "end_line": 77, "complexity": 1, "has_docstring": false, "content_length": 385, "token_count": 81, "document": "function main"}, "abe2664263cc0be3": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "d0c7eddb53c7526e": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 31, "token_count": 8, "document": "import imports"}, "784560674863037b": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 29, "token_count": 10, "document": "import imports"}, "5528885284c061e5": {"name": "TextAnalyzer", "type": "class", "parent": null, "start_line": 5, "end_line": 8, "complexity": 0, "has_docstring": true, "content_length": 99, "token_count": 34, "document": "class TextAnalyzer"}, "0a02fd4592019536": {"name": "__init__", "type": "function", "parent": null, "start_line": 10, "end_line": 11, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 15, "document": "function __init__"}, "9e07ae31ee34a8a7": {"name": "count_words", "type": "function", "parent": null, "start_line": 14, "end_line": 24, "complexity": 1, "has_docstring": true, "content_length": 291, "token_count": 110, "document": "function count_words"}, "1a1737d41a0172f2": {"name": "count_sentences", "type": "function", "parent": null, "start_line": 27, "end_line": 37, "complexity": 1, "has_docstring": true, "content_length": 305, "token_count": 111, "document": "function count_sentences"}, "6338f6b28f078d63": {"name": "count_paragraphs", "type": "function", "parent": null, "start_line": 40, "end_line": 50, "complexity": 1, "has_docstring": true, "content_length": 314, "token_count": 117, "document": "function count_paragraphs"}, "f5067c31813840a4": {"name": "analyze_text", "type": "function", "parent": null, "start_line": 53, "end_line": 67, "complexity": 1, "has_docstring": true, "content_length": 525, "token_count": 156, "document": "function analyze_text"}, "b359bf7c66c948c6": {"name": "SentimentAnalyzer", "type": "class", "parent": null, "start_line": 69, "end_line": 72, "complexity": 0, "has_docstring": true, "content_length": 120, "token_count": 42, "document": "class SentimentAnalyzer"}, "afcf8044ff9d594a": {"name": "__init__", "type": "function", "parent": null, "start_line": 74, "end_line": 75, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 15, "document": "function __init__"}, "098a27f732db5497": {"name": "score_sentiment", "type": "function", "parent": null, "start_line": 78, "end_line": 89, "complexity": 1, "has_docstring": true, "content_length": 393, "token_count": 140, "document": "function score_sentiment"}, "e4cddf350a358f5e": {"name": "TextFormatter", "type": "class", "parent": null, "start_line": 91, "end_line": 94, "complexity": 0, "has_docstring": true, "content_length": 85, "token_count": 32, "document": "class TextFormatter"}, "7a59d0661196bc41": {"name": "__init__", "type": "function", "parent": null, "start_line": 96, "end_line": 97, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 15, "document": "function __init__"}, "44f74f15547f5f9d": {"name": "format_text", "type": "function", "parent": null, "start_line": 100, "end_line": 112, "complexity": 1, "has_docstring": true, "content_length": 376, "token_count": 130, "document": "function format_text"}, "5c20ef621ccf316c": {"name": "TextProcessor", "type": "class", "parent": null, "start_line": 114, "end_line": 117, "complexity": 0, "has_docstring": true, "content_length": 101, "token_count": 34, "document": "class TextProcessor"}, "acf14a5006a63c5c": {"name": "__init__", "type": "function", "parent": null, "start_line": 119, "end_line": 122, "complexity": 1, "has_docstring": false, "content_length": 162, "token_count": 41, "document": "function __init__"}, "77d32ab1cb371cb8": {"name": "process_text", "type": "function", "parent": null, "start_line": 124, "end_line": 137, "complexity": 1, "has_docstring": true, "content_length": 526, "token_count": 161, "document": "function process_text"}, "3c1493593a744856": {"name": "calculate_circle_area", "type": "function", "parent": null, "start_line": 3, "end_line": 19, "complexity": 3, "has_docstring": true, "content_length": 449, "token_count": 138, "document": "function calculate_circle_area"}, "384be46e20cf16e6": {"name": "reverse_string_and_count_vowels", "type": "function", "parent": null, "start_line": 1, "end_line": 19, "complexity": 3, "has_docstring": true, "content_length": 589, "token_count": 196, "document": "function reverse_string_and_count_vowels"}, "5515027945404b2b": {"name": "find_second_largest", "type": "function", "parent": null, "start_line": 1, "end_line": 14, "complexity": 2, "has_docstring": true, "content_length": 379, "token_count": 131, "document": "function find_second_largest"}, "22441518e5d5fd29": {"name": "bubble_sort", "type": "function", "parent": null, "start_line": 1, "end_line": 16, "complexity": 4, "has_docstring": true, "content_length": 426, "token_count": 161, "document": "function bubble_sort"}, "a37a4b4f4475a164": {"name": "<PERSON><PERSON>", "type": "class", "parent": null, "start_line": 1, "end_line": 13, "complexity": 0, "has_docstring": true, "content_length": 505, "token_count": 225, "document": "class Stack"}, "850ca67914b50fd6": {"name": "__init__", "type": "function", "parent": null, "start_line": 15, "end_line": 16, "complexity": 1, "has_docstring": false, "content_length": 47, "token_count": 18, "document": "function __init__"}, "d939a509ca3cfc1e": {"name": "push", "type": "function", "parent": null, "start_line": 18, "end_line": 20, "complexity": 1, "has_docstring": true, "content_length": 108, "token_count": 41, "document": "function push"}, "ef8399ae7279a6ec": {"name": "pop", "type": "function", "parent": null, "start_line": 22, "end_line": 26, "complexity": 2, "has_docstring": true, "content_length": 196, "token_count": 61, "document": "function pop"}, "9a7d21274fc94144": {"name": "peek", "type": "function", "parent": null, "start_line": 28, "end_line": 32, "complexity": 2, "has_docstring": true, "content_length": 204, "token_count": 64, "document": "function peek"}, "1bd4350399110b2e": {"name": "is_empty", "type": "function", "parent": null, "start_line": 34, "end_line": 36, "complexity": 1, "has_docstring": true, "content_length": 102, "token_count": 38, "document": "function is_empty"}, "94327b5c89a077d3": {"name": "size", "type": "function", "parent": null, "start_line": 38, "end_line": 40, "complexity": 1, "has_docstring": true, "content_length": 105, "token_count": 37, "document": "function size"}, "735a13278e71af9f": {"name": "Calculator", "type": "class", "parent": null, "start_line": 2, "end_line": 3, "complexity": 0, "has_docstring": true, "content_length": 89, "token_count": 31, "document": "class Calculator"}, "fe8f4fea26cccfd8": {"name": "__init__", "type": "function", "parent": null, "start_line": 5, "end_line": 6, "complexity": 1, "has_docstring": false, "content_length": 47, "token_count": 19, "document": "function __init__"}, "02125e67c2bae067": {"name": "add", "type": "function", "parent": null, "start_line": 8, "end_line": 15, "complexity": 2, "has_docstring": true, "content_length": 245, "token_count": 63, "document": "function add"}, "8044e0828832149d": {"name": "subtract", "type": "function", "parent": null, "start_line": 17, "end_line": 24, "complexity": 2, "has_docstring": true, "content_length": 258, "token_count": 66, "document": "function subtract"}, "295f6365d5ee2a46": {"name": "multiply", "type": "function", "parent": null, "start_line": 26, "end_line": 33, "complexity": 2, "has_docstring": true, "content_length": 255, "token_count": 63, "document": "function multiply"}, "f0328ba108d3613f": {"name": "divide", "type": "function", "parent": null, "start_line": 35, "end_line": 42, "complexity": 2, "has_docstring": true, "content_length": 252, "token_count": 66, "document": "function divide"}, "676bfa40fb0f1f7c": {"name": "power", "type": "function", "parent": null, "start_line": 44, "end_line": 51, "complexity": 2, "has_docstring": true, "content_length": 249, "token_count": 67, "document": "function power"}, "8d895db619d7fc29": {"name": "square_root", "type": "function", "parent": null, "start_line": 53, "end_line": 60, "complexity": 2, "has_docstring": true, "content_length": 258, "token_count": 67, "document": "function square_root"}, "ee85c460dcf4c0d6": {"name": "factorial", "type": "function", "parent": null, "start_line": 62, "end_line": 69, "complexity": 2, "has_docstring": true, "content_length": 257, "token_count": 64, "document": "function factorial"}, "a60f28c94453ad15": {"name": "calculate_square_root", "type": "function", "parent": null, "start_line": 3, "end_line": 19, "complexity": 2, "has_docstring": true, "content_length": 470, "token_count": 147, "document": "function calculate_square_root"}, "8953ad7fd26525e1": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "81027bc7b9c98c4f": {"name": "bubble_sort", "type": "function", "parent": null, "start_line": 3, "end_line": 10, "complexity": 4, "has_docstring": true, "content_length": 230, "token_count": 81, "document": "function bubble_sort"}, "66992c6686672794": {"name": "selection_sort", "type": "function", "parent": null, "start_line": 12, "end_line": 21, "complexity": 4, "has_docstring": true, "content_length": 286, "token_count": 88, "document": "function selection_sort"}, "cb477b919b70fff3": {"name": "sort_comparison", "type": "function", "parent": null, "start_line": 23, "end_line": 33, "complexity": 1, "has_docstring": true, "content_length": 400, "token_count": 108, "document": "function sort_comparison"}, "ba3fe2397987b84c": {"name": "BinarySearchTree", "type": "class", "parent": null, "start_line": 1, "end_line": 26, "complexity": 0, "has_docstring": true, "content_length": 1116, "token_count": 413, "document": "class BinarySearchTree"}, "5bcce8e47f9b8d63": {"name": "__init__", "type": "function", "parent": null, "start_line": 28, "end_line": 29, "complexity": 1, "has_docstring": false, "content_length": 48, "token_count": 18, "document": "function __init__"}, "30e0b4f4db77a440": {"name": "insert", "type": "function", "parent": null, "start_line": 31, "end_line": 51, "complexity": 7, "has_docstring": true, "content_length": 902, "token_count": 156, "document": "function insert"}, "dbc252a564b5d97f": {"name": "search", "type": "function", "parent": null, "start_line": 53, "end_line": 71, "complexity": 7, "has_docstring": true, "content_length": 698, "token_count": 125, "document": "function search"}, "b826b4f8a2778ac8": {"name": "delete", "type": "function", "parent": null, "start_line": 73, "end_line": 115, "complexity": 18, "has_docstring": true, "content_length": 1957, "token_count": 325, "document": "function delete"}, "e73821c9c09ec860": {"name": "in_order_traversal", "type": "function", "parent": null, "start_line": 117, "end_line": 131, "complexity": 5, "has_docstring": true, "content_length": 524, "token_count": 114, "document": "function in_order_traversal"}, "715d8c1eb155ccc2": {"name": "pre_order_traversal", "type": "function", "parent": null, "start_line": 133, "end_line": 145, "complexity": 4, "has_docstring": true, "content_length": 487, "token_count": 112, "document": "function pre_order_traversal"}, "c80aba2bfa518060": {"name": "post_order_traversal", "type": "function", "parent": null, "start_line": 147, "end_line": 161, "complexity": 5, "has_docstring": true, "content_length": 527, "token_count": 114, "document": "function post_order_traversal"}, "70545d0b0b40dde9": {"name": "find_minimum", "type": "function", "parent": null, "start_line": 163, "end_line": 171, "complexity": 3, "has_docstring": true, "content_length": 342, "token_count": 79, "document": "function find_minimum"}, "f9f7a07dfb406720": {"name": "find_maximum", "type": "function", "parent": null, "start_line": 173, "end_line": 181, "complexity": 3, "has_docstring": true, "content_length": 344, "token_count": 79, "document": "function find_maximum"}, "5b9a35363bd9ef00": {"name": "tree_height", "type": "function", "parent": null, "start_line": 183, "end_line": 190, "complexity": 2, "has_docstring": true, "content_length": 314, "token_count": 79, "document": "function tree_height"}, "adc1b0b42cf822d6": {"name": "Node", "type": "class", "parent": null, "start_line": 20, "end_line": 21, "complexity": 0, "has_docstring": true, "content_length": 63, "token_count": 28, "document": "class Node"}, "741b2ab313c54c68": {"name": "__init__", "type": "function", "parent": null, "start_line": 23, "end_line": 26, "complexity": 1, "has_docstring": false, "content_length": 124, "token_count": 32, "document": "function __init__"}, "505e14ac4de1ee65": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 16, "token_count": 6, "document": "import imports"}, "3b1669f1fd418368": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 12, "token_count": 6, "document": "import imports"}, "dcaa2078ff58fc7d": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "e83fad9c7f39d3a8": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "54ec913109467ca1": {"name": "WebScraperSimulator", "type": "class", "parent": null, "start_line": 6, "end_line": 10, "complexity": 0, "has_docstring": true, "content_length": 232, "token_count": 87, "document": "class WebScraperSimulator"}, "71195bcf2e62f11f": {"name": "__init__", "type": "function", "parent": null, "start_line": 12, "end_line": 16, "complexity": 1, "has_docstring": false, "content_length": 145, "token_count": 39, "document": "function __init__"}, "02f620fc6e8e1f51": {"name": "start", "type": "function", "parent": null, "start_line": 18, "end_line": 25, "complexity": 3, "has_docstring": false, "content_length": 267, "token_count": 57, "document": "function start"}, "794512c5362b2cc4": {"name": "scrape_url", "type": "function", "parent": null, "start_line": 27, "end_line": 40, "complexity": 3, "has_docstring": false, "content_length": 510, "token_count": 112, "document": "function scrape_url"}, "7dd91ecf90d137cd": {"name": "join", "type": "function", "parent": null, "start_line": 42, "end_line": 44, "complexity": 2, "has_docstring": false, "content_length": 85, "token_count": 21, "document": "function join"}, "53b557023d5b028c": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 8, "document": "import imports"}, "6dec2edb4f6a4c5c": {"name": "MatrixOperations", "type": "class", "parent": null, "start_line": 3, "end_line": 6, "complexity": 0, "has_docstring": true, "content_length": 133, "token_count": 44, "document": "class MatrixOperations"}, "ba85f26571476f21": {"name": "__init__", "type": "function", "parent": null, "start_line": 8, "end_line": 9, "complexity": 1, "has_docstring": false, "content_length": 60, "token_count": 20, "document": "function __init__"}, "697a3043dfd074b4": {"name": "add", "type": "function", "parent": null, "start_line": 11, "end_line": 21, "complexity": 1, "has_docstring": true, "content_length": 343, "token_count": 102, "document": "function add"}, "7c9eea64fcc4c0b8": {"name": "multiply", "type": "function", "parent": null, "start_line": 23, "end_line": 33, "complexity": 1, "has_docstring": true, "content_length": 370, "token_count": 106, "document": "function multiply"}, "6203e53de1cf9697": {"name": "determinant", "type": "function", "parent": null, "start_line": 35, "end_line": 42, "complexity": 1, "has_docstring": true, "content_length": 208, "token_count": 61, "document": "function determinant"}, "58b61d7814400faa": {"name": "inverse", "type": "function", "parent": null, "start_line": 44, "end_line": 51, "complexity": 1, "has_docstring": true, "content_length": 225, "token_count": 65, "document": "function inverse"}, "95ccccb642ed436b": {"name": "VectorOperations", "type": "class", "parent": null, "start_line": 53, "end_line": 56, "complexity": 0, "has_docstring": true, "content_length": 137, "token_count": 48, "document": "class VectorOperations"}, "ea05edb309d43baf": {"name": "__init__", "type": "function", "parent": null, "start_line": 58, "end_line": 59, "complexity": 1, "has_docstring": false, "content_length": 60, "token_count": 20, "document": "function __init__"}, "c3b0bc43a0601f44": {"name": "dot_product", "type": "function", "parent": null, "start_line": 61, "end_line": 71, "complexity": 1, "has_docstring": true, "content_length": 356, "token_count": 110, "document": "function dot_product"}, "3f7f3c841b12045a": {"name": "cross_product", "type": "function", "parent": null, "start_line": 73, "end_line": 83, "complexity": 1, "has_docstring": true, "content_length": 393, "token_count": 114, "document": "function cross_product"}, "ef2e780b95ddf068": {"name": "normalize", "type": "function", "parent": null, "start_line": 85, "end_line": 92, "complexity": 1, "has_docstring": true, "content_length": 245, "token_count": 74, "document": "function normalize"}, "e39beea57ddf29f5": {"name": "NumericalMethods", "type": "class", "parent": null, "start_line": 94, "end_line": 97, "complexity": 0, "has_docstring": true, "content_length": 166, "token_count": 60, "document": "class NumericalMethods"}, "8f101360c8a80a14": {"name": "__init__", "type": "function", "parent": null, "start_line": 99, "end_line": 100, "complexity": 1, "has_docstring": false, "content_length": 66, "token_count": 20, "document": "function __init__"}, "719ace9f0a65671f": {"name": "newton_rap<PERSON>on", "type": "function", "parent": null, "start_line": 102, "end_line": 120, "complexity": 3, "has_docstring": true, "content_length": 692, "token_count": 250, "document": "function newton_raphson"}, "78987a9d770d17ce": {"name": "simpson_integration", "type": "function", "parent": null, "start_line": 122, "end_line": 137, "complexity": 1, "has_docstring": true, "content_length": 601, "token_count": 234, "document": "function simpson_integration"}, "5640f6e619ea77df": {"name": "Queue", "type": "class", "parent": null, "start_line": 1, "end_line": 23, "complexity": 0, "has_docstring": true, "content_length": 664, "token_count": 259, "document": "class Queue"}, "64d0f933a4d2ae61": {"name": "__init__", "type": "function", "parent": null, "start_line": 25, "end_line": 27, "complexity": 1, "has_docstring": false, "content_length": 95, "token_count": 26, "document": "function __init__"}, "4df84f17310369e9": {"name": "enqueue", "type": "function", "parent": null, "start_line": 29, "end_line": 39, "complexity": 4, "has_docstring": true, "content_length": 390, "token_count": 87, "document": "function enqueue"}, "8899093f67498cbd": {"name": "dequeue", "type": "function", "parent": null, "start_line": 41, "end_line": 45, "complexity": 2, "has_docstring": true, "content_length": 197, "token_count": 59, "document": "function dequeue"}, "ec99534a0d53076b": {"name": "peek", "type": "function", "parent": null, "start_line": 47, "end_line": 51, "complexity": 2, "has_docstring": true, "content_length": 199, "token_count": 60, "document": "function peek"}, "c196c9393341c1c6": {"name": "is_empty", "type": "function", "parent": null, "start_line": 53, "end_line": 55, "complexity": 1, "has_docstring": true, "content_length": 101, "token_count": 38, "document": "function is_empty"}, "d923c209e5046611": {"name": "size", "type": "function", "parent": null, "start_line": 57, "end_line": 59, "complexity": 1, "has_docstring": true, "content_length": 107, "token_count": 37, "document": "function size"}, "a88c10efa0a846f5": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "8e52127db8f5ce78": {"name": "InputValidator", "type": "class", "parent": null, "start_line": 3, "end_line": 12, "complexity": 0, "has_docstring": true, "content_length": 432, "token_count": 157, "document": "class InputValidator"}, "64d202ee32099211": {"name": "__init__", "type": "function", "parent": null, "start_line": 14, "end_line": 18, "complexity": 1, "has_docstring": false, "content_length": 340, "token_count": 157, "document": "function __init__"}, "e256ea15ffc3f906": {"name": "validate_email", "type": "function", "parent": null, "start_line": 20, "end_line": 30, "complexity": 1, "has_docstring": true, "content_length": 309, "token_count": 101, "document": "function validate_email"}, "537be87928c99902": {"name": "validate_phone", "type": "function", "parent": null, "start_line": 32, "end_line": 42, "complexity": 1, "has_docstring": true, "content_length": 313, "token_count": 103, "document": "function validate_phone"}, "b19261a9fa2d09ce": {"name": "validate_credit_card", "type": "function", "parent": null, "start_line": 44, "end_line": 54, "complexity": 1, "has_docstring": true, "content_length": 361, "token_count": 116, "document": "function validate_credit_card"}, "cda58525e7d4c1bb": {"name": "validate_url", "type": "function", "parent": null, "start_line": 56, "end_line": 66, "complexity": 1, "has_docstring": true, "content_length": 276, "token_count": 97, "document": "function validate_url"}, "8583e7f9fd619d46": {"name": "validate_input", "type": "function", "parent": null, "start_line": 68, "end_line": 81, "complexity": 3, "has_docstring": true, "content_length": 496, "token_count": 145, "document": "function validate_input"}, "b985f68396715bc3": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "16ed149196376ca5": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 7, "document": "import imports"}, "774761c996b0ec44": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 29, "token_count": 10, "document": "import imports"}, "3b14bb3e4563225c": {"name": "PerformanceBenchmarking", "type": "class", "parent": null, "start_line": 5, "end_line": 8, "complexity": 0, "has_docstring": true, "content_length": 107, "token_count": 36, "document": "class PerformanceBenchmarking"}, "8ec3bbc1aa4aef90": {"name": "__init__", "type": "function", "parent": null, "start_line": 10, "end_line": 17, "complexity": 1, "has_docstring": false, "content_length": 249, "token_count": 61, "document": "function __init__"}, "98dba3a48d85b0c3": {"name": "start", "type": "function", "parent": null, "start_line": 19, "end_line": 29, "complexity": 1, "has_docstring": true, "content_length": 507, "token_count": 147, "document": "function start"}, "2c584233e578696a": {"name": "end", "type": "function", "parent": null, "start_line": 31, "end_line": 37, "complexity": 1, "has_docstring": true, "content_length": 284, "token_count": 72, "document": "function end"}, "1a972326e2741ec2": {"name": "get_performance_report", "type": "function", "parent": null, "start_line": 39, "end_line": 51, "complexity": 1, "has_docstring": true, "content_length": 485, "token_count": 119, "document": "function get_performance_report"}, "841c7f6055eabe2b": {"name": "timeit", "type": "function", "parent": null, "start_line": 53, "end_line": 63, "complexity": 1, "has_docstring": true, "content_length": 335, "token_count": 108, "document": "function timeit"}, "7bab09ca7d20d6d8": {"name": "profile", "type": "function", "parent": null, "start_line": 65, "end_line": 75, "complexity": 1, "has_docstring": true, "content_length": 365, "token_count": 119, "document": "function profile"}, "4f859e9fb69a84c3": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 8, "document": "import imports"}, "e6b1f7aa87d74da5": {"name": "helper_function", "type": "function", "parent": null, "start_line": 1, "end_line": 5, "complexity": 1, "has_docstring": true, "content_length": 132, "token_count": 47, "document": "function helper_function"}, "3265e596e4c22eeb": {"name": "main", "type": "function", "parent": null, "start_line": 7, "end_line": 15, "complexity": 2, "has_docstring": true, "content_length": 239, "token_count": 70, "document": "function main"}, "f1624ed4e022e075": {"name": "Calculator", "type": "class", "parent": null, "start_line": 1, "end_line": 2, "complexity": 0, "has_docstring": true, "content_length": 123, "token_count": 43, "document": "class Calculator"}, "fdc4571c420fe62b": {"name": "__init__", "type": "function", "parent": null, "start_line": 4, "end_line": 5, "complexity": 1, "has_docstring": false, "content_length": 47, "token_count": 19, "document": "function __init__"}, "0f7b749df90d7117": {"name": "add", "type": "function", "parent": null, "start_line": 7, "end_line": 9, "complexity": 1, "has_docstring": true, "content_length": 85, "token_count": 32, "document": "function add"}, "b53ec07afe4cab0d": {"name": "subtract", "type": "function", "parent": null, "start_line": 11, "end_line": 13, "complexity": 1, "has_docstring": true, "content_length": 98, "token_count": 35, "document": "function subtract"}, "2b45142b9cba65c6": {"name": "multiply", "type": "function", "parent": null, "start_line": 15, "end_line": 17, "complexity": 1, "has_docstring": true, "content_length": 95, "token_count": 32, "document": "function multiply"}, "bfeaa296973e8657": {"name": "divide", "type": "function", "parent": null, "start_line": 19, "end_line": 23, "complexity": 2, "has_docstring": true, "content_length": 166, "token_count": 51, "document": "function divide"}, "2506145a6100721f": {"name": "main", "type": "function", "parent": null, "start_line": 1, "end_line": 8, "complexity": 2, "has_docstring": true, "content_length": 209, "token_count": 63, "document": "function main"}, "b326f7b0cd8f5685": {"name": "calculate_result", "type": "function", "parent": null, "start_line": 1, "end_line": 18, "complexity": 2, "has_docstring": true, "content_length": 613, "token_count": 180, "document": "function calculate_result"}, "6297d304ee6fbe39": {"name": "main", "type": "function", "parent": null, "start_line": 1, "end_line": 4, "complexity": 1, "has_docstring": false, "content_length": 154, "token_count": 39, "document": "function main"}, "9479235d5549eb8f": {"name": "undefined_function", "type": "function", "parent": null, "start_line": 1, "end_line": 10, "complexity": 1, "has_docstring": true, "content_length": 227, "token_count": 90, "document": "function undefined_function"}, "6e8d8cca8da101f1": {"name": "Calculator", "type": "class", "parent": null, "start_line": 2, "end_line": 3, "complexity": 0, "has_docstring": true, "content_length": 98, "token_count": 37, "document": "class Calculator"}, "257c33dc4b64e930": {"name": "add", "type": "function", "parent": null, "start_line": 5, "end_line": 7, "complexity": 1, "has_docstring": true, "content_length": 109, "token_count": 40, "document": "function add"}, "cb6274f439b7feac": {"name": "multiply", "type": "function", "parent": null, "start_line": 9, "end_line": 11, "complexity": 1, "has_docstring": true, "content_length": 120, "token_count": 42, "document": "function multiply"}, "e8c36b1db06b1768": {"name": "Game", "type": "class", "parent": null, "start_line": 1, "end_line": 2, "complexity": 0, "has_docstring": true, "content_length": 42, "token_count": 19, "document": "class Game"}, "e798c8aad3e8fd94": {"name": "__init__", "type": "function", "parent": null, "start_line": 4, "end_line": 19, "complexity": 1, "has_docstring": false, "content_length": 473, "token_count": 144, "document": "function __init__"}, "cd9ef25e9e6bfb77": {"name": "render", "type": "function", "parent": null, "start_line": 21, "end_line": 38, "complexity": 4, "has_docstring": true, "content_length": 590, "token_count": 151, "document": "function render"}, "d177889229cb227f": {"name": "handle_input", "type": "function", "parent": null, "start_line": 40, "end_line": 72, "complexity": 13, "has_docstring": true, "content_length": 1130, "token_count": 283, "document": "function handle_input"}, "40fb0df70b89abcb": {"name": "main", "type": "function", "parent": null, "start_line": 74, "end_line": 98, "complexity": 1, "has_docstring": false, "content_length": 685, "token_count": 190, "document": "function main"}, "591439baed5c8280": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 6, "document": "import imports"}, "5b64e9527c2b7903": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "d74238d31a3be151": {"name": "DataAnalyzer", "type": "class", "parent": null, "start_line": 4, "end_line": 5, "complexity": 0, "has_docstring": true, "content_length": 74, "token_count": 27, "document": "class DataAnalyzer"}, "b614bb40d2ba71d4": {"name": "__init__", "type": "function", "parent": null, "start_line": 7, "end_line": 8, "complexity": 1, "has_docstring": false, "content_length": 46, "token_count": 18, "document": "function __init__"}, "2596ccce021e808e": {"name": "load_csv", "type": "function", "parent": null, "start_line": 10, "end_line": 18, "complexity": 3, "has_docstring": true, "content_length": 380, "token_count": 97, "document": "function load_csv"}, "4b6010b2b4947721": {"name": "analyze_data", "type": "function", "parent": null, "start_line": 20, "end_line": 52, "complexity": 8, "has_docstring": true, "content_length": 919, "token_count": 172, "document": "function analyze_data"}, "7ad77d9dfa80e6a2": {"name": "main", "type": "function", "parent": null, "start_line": 54, "end_line": 88, "complexity": 5, "has_docstring": false, "content_length": 1117, "token_count": 315, "document": "function main"}, "ab8eb1fdac83e96b": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 6, "document": "import imports"}, "0d518649f0bdabde": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "58c53323b9df1123": {"name": "create_dataset", "type": "function", "parent": null, "start_line": 4, "end_line": 19, "complexity": 2, "has_docstring": true, "content_length": 390, "token_count": 120, "document": "function create_dataset"}, "44db68a3a2bb09d5": {"name": "NeuralNetwork", "type": "class", "parent": null, "start_line": 21, "end_line": 22, "complexity": 0, "has_docstring": true, "content_length": 81, "token_count": 29, "document": "class NeuralNetwork"}, "7bdca70a17498ef0": {"name": "__init__", "type": "function", "parent": null, "start_line": 24, "end_line": 34, "complexity": 1, "has_docstring": false, "content_length": 603, "token_count": 164, "document": "function __init__"}, "b8b1fac415a81b97": {"name": "sigmoid", "type": "function", "parent": null, "start_line": 36, "end_line": 38, "complexity": 1, "has_docstring": true, "content_length": 127, "token_count": 46, "document": "function sigmoid"}, "e0fd64b5e665fa64": {"name": "forward", "type": "function", "parent": null, "start_line": 40, "end_line": 54, "complexity": 3, "has_docstring": true, "content_length": 593, "token_count": 138, "document": "function forward"}, "fc6376ee2f58d3b8": {"name": "train_step", "type": "function", "parent": null, "start_line": 56, "end_line": 71, "complexity": 4, "has_docstring": true, "content_length": 698, "token_count": 167, "document": "function train_step"}, "f0aafcd791ec1d16": {"name": "train_model", "type": "function", "parent": null, "start_line": 73, "end_line": 93, "complexity": 4, "has_docstring": true, "content_length": 784, "token_count": 205, "document": "function train_model"}, "a38e957ec1943726": {"name": "evaluate_model", "type": "function", "parent": null, "start_line": 95, "end_line": 106, "complexity": 3, "has_docstring": true, "content_length": 400, "token_count": 112, "document": "function evaluate_model"}, "d61f022c74225f30": {"name": "main", "type": "function", "parent": null, "start_line": 108, "end_line": 123, "complexity": 1, "has_docstring": false, "content_length": 500, "token_count": 127, "document": "function main"}, "16385e935b5348b8": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 21, "token_count": 7, "document": "import imports"}, "fc6269d4c12c1926": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 19, "token_count": 7, "document": "import imports"}, "7e6e8ef6429c61a0": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 6, "document": "import imports"}, "91d609cd9052ee03": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 6, "document": "import imports"}, "6fbe1abb55c0013c": {"name": "WebScraper", "type": "class", "parent": null, "start_line": 6, "end_line": 7, "complexity": 0, "has_docstring": true, "content_length": 70, "token_count": 29, "document": "class WebScraper"}, "686c06c95f1f8ced": {"name": "__init__", "type": "function", "parent": null, "start_line": 9, "end_line": 12, "complexity": 1, "has_docstring": false, "content_length": 147, "token_count": 51, "document": "function __init__"}, "a5f2beecede0a395": {"name": "fetch_url", "type": "function", "parent": null, "start_line": 14, "end_line": 22, "complexity": 3, "has_docstring": true, "content_length": 396, "token_count": 91, "document": "function fetch_url"}, "2a65e4ff8ec793de": {"name": "parse_content", "type": "function", "parent": null, "start_line": 24, "end_line": 43, "complexity": 2, "has_docstring": true, "content_length": 680, "token_count": 186, "document": "function parse_content"}, "08738dc3ef9e60a1": {"name": "main", "type": "function", "parent": null, "start_line": 45, "end_line": 74, "complexity": 5, "has_docstring": false, "content_length": 860, "token_count": 223, "document": "function main"}, "b09b136bdbe93be5": {"name": "Calculator", "type": "class", "parent": null, "start_line": 1, "end_line": 2, "complexity": 0, "has_docstring": true, "content_length": 76, "token_count": 27, "document": "class Calculator"}, "738fc0712dbaa867": {"name": "add", "type": "function", "parent": null, "start_line": 4, "end_line": 6, "complexity": 1, "has_docstring": true, "content_length": 76, "token_count": 30, "document": "function add"}, "546cb70fb599ba42": {"name": "subtract", "type": "function", "parent": null, "start_line": 8, "end_line": 10, "complexity": 1, "has_docstring": true, "content_length": 83, "token_count": 33, "document": "function subtract"}, "8395ce9b76732371": {"name": "multiply", "type": "function", "parent": null, "start_line": 12, "end_line": 14, "complexity": 1, "has_docstring": true, "content_length": 86, "token_count": 30, "document": "function multiply"}, "74b5b9ca6bb561b5": {"name": "divide", "type": "function", "parent": null, "start_line": 16, "end_line": 20, "complexity": 2, "has_docstring": true, "content_length": 150, "token_count": 49, "document": "function divide"}, "83795f21351697ab": {"name": "power", "type": "function", "parent": null, "start_line": 22, "end_line": 24, "complexity": 1, "has_docstring": true, "content_length": 130, "token_count": 40, "document": "function power"}, "3d8561749b843632": {"name": "Calculator", "type": "class", "parent": null, "start_line": 1, "end_line": 2, "complexity": 0, "has_docstring": true, "content_length": 76, "token_count": 27, "document": "class Calculator"}, "435812bc655b3178": {"name": "add", "type": "function", "parent": null, "start_line": 4, "end_line": 6, "complexity": 1, "has_docstring": true, "content_length": 76, "token_count": 30, "document": "function add"}, "0ca428e4079a3f31": {"name": "subtract", "type": "function", "parent": null, "start_line": 8, "end_line": 10, "complexity": 1, "has_docstring": true, "content_length": 83, "token_count": 33, "document": "function subtract"}, "2dd1a2a34a9e3e56": {"name": "multiply", "type": "function", "parent": null, "start_line": 12, "end_line": 14, "complexity": 1, "has_docstring": true, "content_length": 86, "token_count": 30, "document": "function multiply"}, "f48997a572151ed3": {"name": "divide", "type": "function", "parent": null, "start_line": 16, "end_line": 20, "complexity": 2, "has_docstring": true, "content_length": 150, "token_count": 49, "document": "function divide"}, "a65789e51932ff90": {"name": "power", "type": "function", "parent": null, "start_line": 22, "end_line": 24, "complexity": 1, "has_docstring": true, "content_length": 130, "token_count": 40, "document": "function power"}, "96e3f5968f72ed3e": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 6, "document": "import imports"}, "bad709d5eae99eb1": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 9, "document": "import imports"}, "dcb1c00b4fad2eb1": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 9, "document": "import imports"}, "10535d6a2cf7ff72": {"name": "main", "type": "function", "parent": null, "start_line": 8, "end_line": 13, "complexity": 1, "has_docstring": true, "content_length": 172, "token_count": 50, "document": "function main"}, "5a4a2f466cede0aa": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 27, "token_count": 8, "document": "import imports"}, "94dcbbedf8feab1a": {"name": "User", "type": "class", "parent": null, "start_line": 5, "end_line": 6, "complexity": 0, "has_docstring": true, "content_length": 55, "token_count": 23, "document": "class User"}, "fa3f42a106bf25d7": {"name": "__init__", "type": "function", "parent": null, "start_line": 8, "end_line": 12, "complexity": 1, "has_docstring": true, "content_length": 179, "token_count": 56, "document": "function __init__"}, "d42dda61705a148c": {"name": "get_info", "type": "function", "parent": null, "start_line": 14, "end_line": 20, "complexity": 1, "has_docstring": true, "content_length": 186, "token_count": 52, "document": "function get_info"}, "cb09766b27b5373f": {"name": "is_adult", "type": "function", "parent": null, "start_line": 22, "end_line": 24, "complexity": 1, "has_docstring": true, "content_length": 102, "token_count": 41, "document": "function is_adult"}, "8079eb661768816e": {"name": "helper_function", "type": "function", "parent": null, "start_line": 3, "end_line": 5, "complexity": 1, "has_docstring": true, "content_length": 123, "token_count": 42, "document": "function helper_function"}, "0e6ed774666ce1b5": {"name": "calculate_age_in_days", "type": "function", "parent": null, "start_line": 7, "end_line": 9, "complexity": 1, "has_docstring": true, "content_length": 101, "token_count": 40, "document": "function calculate_age_in_days"}, "45a016166bb5b069": {"name": "<PERSON><PERSON>", "type": "class", "parent": null, "start_line": 11, "end_line": 12, "complexity": 0, "has_docstring": true, "content_length": 45, "token_count": 19, "document": "class Logger"}, "4c3e2b24839e06e3": {"name": "__init__", "type": "function", "parent": null, "start_line": 14, "end_line": 15, "complexity": 1, "has_docstring": false, "content_length": 71, "token_count": 25, "document": "function __init__"}, "a9cb383e6e72ff99": {"name": "log", "type": "function", "parent": null, "start_line": 17, "end_line": 19, "complexity": 1, "has_docstring": true, "content_length": 104, "token_count": 36, "document": "function log"}, "620387468107ee32": {"name": "greet", "type": "function", "parent": null, "start_line": 1, "end_line": 2, "complexity": 1, "has_docstring": false, "content_length": 39, "token_count": 20, "document": "function greet"}, "2595aa3fc21b08e4": {"name": "add", "type": "function", "parent": null, "start_line": 1, "end_line": 2, "complexity": 1, "has_docstring": false, "content_length": 31, "token_count": 21, "document": "function add"}, "54bb5bd65fc82bf6": {"name": "Calculator", "type": "class", "parent": null, "start_line": 5, "end_line": 6, "complexity": 0, "has_docstring": true, "content_length": 89, "token_count": 35, "document": "class Calculator"}, "04cf0fcd70211f8c": {"name": "__init__", "type": "method", "parent": "Calculator", "start_line": 8, "end_line": 9, "complexity": 1, "has_docstring": false, "content_length": 49, "token_count": 35, "document": "method __init__ in Calculator"}, "1dd0cfe34470e84a": {"name": "add", "type": "method", "parent": "Calculator", "start_line": 11, "end_line": 15, "complexity": 1, "has_docstring": true, "content_length": 175, "token_count": 101, "document": "method add in Calculator"}, "13040b0dd4d92bcc": {"name": "subtract", "type": "method", "parent": "Calculator", "start_line": 17, "end_line": 21, "complexity": 1, "has_docstring": true, "content_length": 182, "token_count": 107, "document": "method subtract in Calculator"}, "a82b8595906b5d0c": {"name": "multiply", "type": "method", "parent": "Calculator", "start_line": 23, "end_line": 27, "complexity": 1, "has_docstring": true, "content_length": 185, "token_count": 105, "document": "method multiply in Calculator"}, "1497a45bc0b45f1f": {"name": "divide", "type": "method", "parent": "Calculator", "start_line": 29, "end_line": 35, "complexity": 2, "has_docstring": true, "content_length": 249, "token_count": 139, "document": "method divide in Calculator"}, "dc09dc41aea5b786": {"name": "get_history", "type": "method", "parent": "Calculator", "start_line": 37, "end_line": 39, "complexity": 1, "has_docstring": true, "content_length": 107, "token_count": 59, "document": "method get_history in Calculator"}, "0c4ec63c9755e43f": {"name": "clear_history", "type": "method", "parent": "Calculator", "start_line": 41, "end_line": 43, "complexity": 1, "has_docstring": true, "content_length": 102, "token_count": 58, "document": "method clear_history in Calculator"}, "8476ccbc9a403028": {"name": "calculate_factorial", "type": "function", "parent": null, "start_line": 46, "end_line": 52, "complexity": 4, "has_docstring": true, "content_length": 258, "token_count": 119, "document": "function calculate_factorial"}, "b22f05b0877aa67c": {"name": "fibonacci_sequence", "type": "function", "parent": null, "start_line": 55, "end_line": 68, "complexity": 5, "has_docstring": true, "content_length": 333, "token_count": 183, "document": "function fibonacci_sequence"}, "8a7ceead224e311e": {"name": "power", "type": "function", "parent": null, "start_line": 71, "end_line": 73, "complexity": 1, "has_docstring": true, "content_length": 112, "token_count": 50, "document": "function power"}, "4f6beaecbdd10a23": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "9e9eefeeaec3bdd8": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 9, "document": "import imports"}, "e074f701f7c8b4bb": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 34, "token_count": 15, "document": "import imports"}, "c35bfd01a10b1d49": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "ee86ff66288d3e5d": {"name": "DataProcessor", "type": "class", "parent": null, "start_line": 11, "end_line": 12, "complexity": 0, "has_docstring": true, "content_length": 84, "token_count": 41, "document": "class DataProcessor"}, "52c8d78fc6049237": {"name": "__init__", "type": "method", "parent": "DataProcessor", "start_line": 14, "end_line": 16, "complexity": 1, "has_docstring": false, "content_length": 108, "token_count": 61, "document": "method __init__ in DataProcessor"}, "50cd7dc7b9753975": {"name": "read_json", "type": "method", "parent": "DataProcessor", "start_line": 18, "end_line": 28, "complexity": 3, "has_docstring": true, "content_length": 507, "token_count": 262, "document": "method read_json in DataProcessor"}, "641498d33afaadd0": {"name": "write_json", "type": "method", "parent": "DataProcessor", "start_line": 30, "end_line": 38, "complexity": 3, "has_docstring": true, "content_length": 389, "token_count": 212, "document": "method write_json in DataProcessor"}, "402bfe09c9045fe5": {"name": "read_csv", "type": "method", "parent": "DataProcessor", "start_line": 40, "end_line": 51, "complexity": 4, "has_docstring": true, "content_length": 555, "token_count": 299, "document": "method read_csv in DataProcessor"}, "78d776aa1969b4e1": {"name": "filter_data", "type": "method", "parent": "DataProcessor", "start_line": 53, "end_line": 55, "complexity": 1, "has_docstring": true, "content_length": 198, "token_count": 102, "document": "method filter_data in DataProcessor"}, "fe83bf6fc22bd9d1": {"name": "aggregate_data", "type": "method", "parent": "DataProcessor", "start_line": 57, "end_line": 87, "complexity": 10, "has_docstring": true, "content_length": 1201, "token_count": 634, "document": "method aggregate_data in DataProcessor"}, "8523c278c9a9a167": {"name": "validate_email", "type": "function", "parent": null, "start_line": 90, "end_line": 94, "complexity": 1, "has_docstring": true, "content_length": 211, "token_count": 112, "document": "function validate_email"}, "e42078e0f5c9ec9d": {"name": "clean_text", "type": "function", "parent": null, "start_line": 97, "end_line": 107, "complexity": 1, "has_docstring": true, "content_length": 313, "token_count": 145, "document": "function clean_text"}, "801b4fbde8425451": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 11, "document": "import imports"}, "5044d3dc12984213": {"name": "generate_sample_data", "type": "function", "parent": null, "start_line": 7, "end_line": 14, "complexity": 1, "has_docstring": true, "content_length": 330, "token_count": 139, "document": "function generate_sample_data"}, "c58691d1348de27a": {"name": "split_data", "type": "function", "parent": null, "start_line": 16, "end_line": 24, "complexity": 1, "has_docstring": true, "content_length": 310, "token_count": 161, "document": "function split_data"}, "fbc8f97046996e14": {"name": "normalize_data", "type": "function", "parent": null, "start_line": 26, "end_line": 34, "complexity": 1, "has_docstring": true, "content_length": 314, "token_count": 165, "document": "function normalize_data"}, "143b7c695ef9b6f9": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 11, "document": "import imports"}, "379838ce46445097": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 83, "token_count": 25, "document": "import imports"}, "c94869b713f936cc": {"name": "NeuralNetwork", "type": "class", "parent": null, "start_line": 5, "end_line": 6, "complexity": 0, "has_docstring": true, "content_length": 89, "token_count": 37, "document": "class NeuralNetwork"}, "cb86d5c18613adbd": {"name": "__init__", "type": "method", "parent": "NeuralNetwork", "start_line": 8, "end_line": 15, "complexity": 1, "has_docstring": false, "content_length": 337, "token_count": 162, "document": "method __init__ in NeuralNetwork"}, "ea0012ab001dc907": {"name": "train", "type": "method", "parent": "NeuralNetwork", "start_line": 17, "end_line": 42, "complexity": 2, "has_docstring": true, "content_length": 1132, "token_count": 527, "document": "method train in NeuralNetwork"}, "edad7083f849147d": {"name": "predict", "type": "method", "parent": "NeuralNetwork", "start_line": 44, "end_line": 48, "complexity": 1, "has_docstring": true, "content_length": 223, "token_count": 111, "document": "method predict in NeuralNetwork"}, "4a9990380447debc": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 83, "token_count": 25, "document": "import imports"}, "c47d8e91a99a92ed": {"name": "__init__", "type": "method", "parent": "NeuralNetwork", "start_line": 7, "end_line": 16, "complexity": 1, "has_docstring": false, "content_length": 470, "token_count": 224, "document": "method __init__ in NeuralNetwork"}, "57cc4db40925a211": {"name": "forward_pass", "type": "method", "parent": "NeuralNetwork", "start_line": 18, "end_line": 28, "complexity": 1, "has_docstring": true, "content_length": 453, "token_count": 200, "document": "method forward_pass in NeuralNetwork"}, "34099ace0ecf2cf3": {"name": "backward_pass", "type": "method", "parent": "NeuralNetwork", "start_line": 30, "end_line": 46, "complexity": 1, "has_docstring": true, "content_length": 956, "token_count": 396, "document": "method backward_pass in NeuralNetwork"}, "4b026967b211a0cb": {"name": "train", "type": "method", "parent": "NeuralNetwork", "start_line": 48, "end_line": 52, "complexity": 2, "has_docstring": true, "content_length": 178, "token_count": 109, "document": "method train in NeuralNetwork"}, "91132e8d554e5254": {"name": "sigmoid", "type": "function", "parent": null, "start_line": 54, "end_line": 56, "complexity": 1, "has_docstring": true, "content_length": 90, "token_count": 53, "document": "function sigmoid"}, "caa487483f6e5460": {"name": "sigmoid_derivative", "type": "function", "parent": null, "start_line": 58, "end_line": 60, "complexity": 1, "has_docstring": true, "content_length": 110, "token_count": 63, "document": "function sigmoid_derivative"}, "0ca706bc865003bf": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 11, "document": "import imports"}, "c5001cdc965a196e": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 71, "token_count": 25, "document": "import imports"}, "8a836042e3c8683c": {"name": "NeuralNetwork", "type": "class", "parent": null, "start_line": 10, "end_line": 11, "complexity": 0, "has_docstring": true, "content_length": 85, "token_count": 35, "document": "class NeuralNetwork"}, "e2467e2811925763": {"name": "__init__", "type": "method", "parent": "NeuralNetwork", "start_line": 13, "end_line": 28, "complexity": 1, "has_docstring": true, "content_length": 682, "token_count": 328, "document": "method __init__ in NeuralNetwork"}, "8890e0cf02332e2e": {"name": "sigmoid", "type": "method", "parent": "NeuralNetwork", "start_line": 30, "end_line": 34, "complexity": 1, "has_docstring": true, "content_length": 175, "token_count": 104, "document": "method sigmoid in NeuralNetwork"}, "68cfa24fd3e9e5c1": {"name": "sigmoid_derivative", "type": "method", "parent": "NeuralNetwork", "start_line": 36, "end_line": 38, "complexity": 1, "has_docstring": true, "content_length": 109, "token_count": 79, "document": "method sigmoid_derivative in NeuralNetwork"}, "9e689341b7b7b4da": {"name": "forward", "type": "method", "parent": "NeuralNetwork", "start_line": 40, "end_line": 46, "complexity": 1, "has_docstring": true, "content_length": 263, "token_count": 161, "document": "method forward in NeuralNetwork"}, "a3c2236d0bc1f9ac": {"name": "compute_loss", "type": "method", "parent": "NeuralNetwork", "start_line": 48, "end_line": 52, "complexity": 1, "has_docstring": true, "content_length": 273, "token_count": 155, "document": "method compute_loss in NeuralNetwork"}, "4bad6e60936d85ea": {"name": "backward", "type": "method", "parent": "NeuralNetwork", "start_line": 54, "end_line": 72, "complexity": 1, "has_docstring": true, "content_length": 706, "token_count": 412, "document": "method backward in NeuralNetwork"}, "35e7720b42d0cdf7": {"name": "train", "type": "method", "parent": "NeuralNetwork", "start_line": 74, "end_line": 103, "complexity": 3, "has_docstring": true, "content_length": 1299, "token_count": 710, "document": "method train in NeuralNetwork"}, "58db9e8fca769191": {"name": "predict", "type": "method", "parent": "NeuralNetwork", "start_line": 105, "end_line": 108, "complexity": 1, "has_docstring": true, "content_length": 154, "token_count": 89, "document": "method predict in NeuralNetwork"}, "9df660ec70cdb2a1": {"name": "accuracy", "type": "method", "parent": "NeuralNetwork", "start_line": 110, "end_line": 112, "complexity": 1, "has_docstring": true, "content_length": 114, "token_count": 69, "document": "method accuracy in NeuralNetwork"}, "1ab08cf16d50f6c8": {"name": "plot_training_history", "type": "method", "parent": "NeuralNetwork", "start_line": 114, "end_line": 134, "complexity": 1, "has_docstring": true, "content_length": 706, "token_count": 390, "document": "method plot_training_history in NeuralNetwork"}, "1086594c4a5603a3": {"name": "main", "type": "function", "parent": null, "start_line": 137, "end_line": 186, "complexity": 2, "has_docstring": true, "content_length": 1627, "token_count": 728, "document": "function main"}, "34dc3d0a16f69d2c": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "f372879bd74e0b65": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 9, "document": "import imports"}, "cc445eea459027af": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "f316d2a0bce499ef": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 42, "token_count": 16, "document": "import imports"}, "78d331932b7bb925": {"name": "create_sample_code_files", "type": "function", "parent": null, "start_line": 6, "end_line": 21, "complexity": 2, "has_docstring": false, "content_length": 544, "token_count": 277, "document": "function create_sample_code_files"}, "beb836db60177008": {"name": "test_codebase_analyzer", "type": "function", "parent": null, "start_line": 23, "end_line": 48, "complexity": 1, "has_docstring": false, "content_length": 1010, "token_count": 386, "document": "function test_codebase_analyzer"}, "1a0e174fdbc94570": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "c8e6f76367333f40": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "d7c669c3fd088b80": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 39, "token_count": 17, "document": "import imports"}, "2d6984fde77f3f94": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 14, "token_count": 9, "document": "import imports"}, "87f0b930ad071e74": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 63, "token_count": 22, "document": "import imports"}, "8550c4040092d653": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 16, "token_count": 9, "document": "import imports"}, "f9697d1ba4967781": {"name": "imports", "type": "import", "parent": null, "start_line": 12, "end_line": 12, "complexity": 0, "has_docstring": false, "content_length": 55, "token_count": 19, "document": "import imports"}, "cf7f94b7057ba7e6": {"name": "imports", "type": "import", "parent": null, "start_line": 13, "end_line": 13, "complexity": 0, "has_docstring": false, "content_length": 65, "token_count": 21, "document": "import imports"}, "89ccde4475339e03": {"name": "imports", "type": "import", "parent": null, "start_line": 14, "end_line": 14, "complexity": 0, "has_docstring": false, "content_length": 61, "token_count": 23, "document": "import imports"}, "3db8ec694c12023b": {"name": "imports", "type": "import", "parent": null, "start_line": 15, "end_line": 15, "complexity": 0, "has_docstring": false, "content_length": 39, "token_count": 13, "document": "import imports"}, "a799fffa3d89e7c8": {"name": "imports", "type": "import", "parent": null, "start_line": 16, "end_line": 16, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 12, "document": "import imports"}, "61e43aeea714643f": {"name": "CodebaseAnalyzer", "type": "class", "parent": null, "start_line": 19, "end_line": 20, "complexity": 0, "has_docstring": true, "content_length": 115, "token_count": 53, "document": "class CodebaseAnalyzer"}, "9272ea4c408c3bd1": {"name": "__init__", "type": "method", "parent": "CodebaseAnalyzer", "start_line": 22, "end_line": 36, "complexity": 1, "has_docstring": false, "content_length": 474, "token_count": 245, "document": "method __init__ in CodebaseAnalyzer"}, "7dceeed67993a41e": {"name": "analyze_path", "type": "method", "parent": "CodebaseAnalyzer", "start_line": 38, "end_line": 102, "complexity": 10, "has_docstring": true, "content_length": 2563, "token_count": 1292, "document": "method analyze_path in CodebaseAnalyzer"}, "312ae7f2a1f34b06": {"name": "_process_file", "type": "method", "parent": "CodebaseAnalyzer", "start_line": 104, "end_line": 152, "complexity": 12, "has_docstring": true, "content_length": 1937, "token_count": 998, "document": "method _process_file in CodebaseAnalyzer"}, "d7e4ad53470fad35": {"name": "_discover_files", "type": "method", "parent": "CodebaseAnalyzer", "start_line": 154, "end_line": 174, "complexity": 8, "has_docstring": true, "content_length": 857, "token_count": 438, "document": "method _discover_files in CodebaseAnalyzer"}, "84180eae7f8c3307": {"name": "_should_process_file", "type": "method", "parent": "CodebaseAnalyzer", "start_line": 176, "end_line": 195, "complexity": 5, "has_docstring": true, "content_length": 646, "token_count": 322, "document": "method _should_process_file in CodebaseAnalyzer"}, "2a2a6671a5913a6e": {"name": "_should_ignore_path", "type": "method", "parent": "CodebaseAnalyzer", "start_line": 197, "end_line": 207, "complexity": 4, "has_docstring": true, "content_length": 369, "token_count": 190, "document": "method _should_ignore_path in CodebaseAnalyzer"}, "c35b64015eb8fe38": {"name": "_reset_stats", "type": "method", "parent": "CodebaseAnalyzer", "start_line": 209, "end_line": 217, "complexity": 1, "has_docstring": true, "content_length": 267, "token_count": 168, "document": "method _reset_stats in CodebaseAnalyzer"}, "4a56ca29ddc952de": {"name": "analyze_single_file", "type": "method", "parent": "CodebaseAnalyzer", "start_line": 219, "end_line": 257, "complexity": 4, "has_docstring": true, "content_length": 1506, "token_count": 837, "document": "method analyze_single_file in CodebaseAnalyzer"}, "d4c902a51ba12d48": {"name": "get_similar_chunks", "type": "method", "parent": "CodebaseAnalyzer", "start_line": 259, "end_line": 262, "complexity": 1, "has_docstring": true, "content_length": 209, "token_count": 108, "document": "method get_similar_chunks in CodebaseAnalyzer"}, "06a237f15bfcee4f": {"name": "get_chunk_details", "type": "method", "parent": "CodebaseAnalyzer", "start_line": 264, "end_line": 292, "complexity": 4, "has_docstring": true, "content_length": 1051, "token_count": 501, "document": "method get_chunk_details in CodebaseAnalyzer"}, "ff761bb7c885bee2": {"name": "imports", "type": "import", "parent": null, "start_line": 12, "end_line": 12, "complexity": 0, "has_docstring": false, "content_length": 12, "token_count": 8, "document": "import imports"}, "b54183065fe8c7c1": {"name": "imports", "type": "import", "parent": null, "start_line": 13, "end_line": 13, "complexity": 0, "has_docstring": false, "content_length": 32, "token_count": 12, "document": "import imports"}, "aaa84c83513b31b1": {"name": "imports", "type": "import", "parent": null, "start_line": 14, "end_line": 14, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 12, "document": "import imports"}, "31d3c0f45e64271b": {"name": "imports", "type": "import", "parent": null, "start_line": 15, "end_line": 15, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 12, "document": "import imports"}, "3472fb0b46b043a3": {"name": "imports", "type": "import", "parent": null, "start_line": 16, "end_line": 16, "complexity": 0, "has_docstring": false, "content_length": 61, "token_count": 19, "document": "import imports"}, "39799da58d3bb3a8": {"name": "imports", "type": "import", "parent": null, "start_line": 19, "end_line": 19, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 8, "document": "import imports"}, "6af5724261f7b87a": {"name": "imports", "type": "import", "parent": null, "start_line": 23, "end_line": 23, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "aee13f8380b5aefc": {"name": "imports", "type": "import", "parent": null, "start_line": 24, "end_line": 24, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "3bb800f6f868530e": {"name": "imports", "type": "import", "parent": null, "start_line": 25, "end_line": 25, "complexity": 0, "has_docstring": false, "content_length": 34, "token_count": 15, "document": "import imports"}, "88a1293ce1612cc0": {"name": "imports", "type": "import", "parent": null, "start_line": 27, "end_line": 27, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 8, "document": "import imports"}, "f4a01fa74738c93a": {"name": "imports", "type": "import", "parent": null, "start_line": 28, "end_line": 28, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "777d2b4f23e52c77": {"name": "imports", "type": "import", "parent": null, "start_line": 31, "end_line": 31, "complexity": 0, "has_docstring": false, "content_length": 42, "token_count": 16, "document": "import imports"}, "62eb28f206492583": {"name": "imports", "type": "import", "parent": null, "start_line": 32, "end_line": 32, "complexity": 0, "has_docstring": false, "content_length": 39, "token_count": 13, "document": "import imports"}, "3a7260ec4f5e51f9": {"name": "imports", "type": "import", "parent": null, "start_line": 33, "end_line": 33, "complexity": 0, "has_docstring": false, "content_length": 46, "token_count": 18, "document": "import imports"}, "ac51d69fcf4b544b": {"name": "imports", "type": "import", "parent": null, "start_line": 34, "end_line": 34, "complexity": 0, "has_docstring": false, "content_length": 47, "token_count": 18, "document": "import imports"}, "1bdd64f04d8f50e3": {"name": "imports", "type": "import", "parent": null, "start_line": 35, "end_line": 35, "complexity": 0, "has_docstring": false, "content_length": 45, "token_count": 17, "document": "import imports"}, "4f0698c64f5ea020": {"name": "imports", "type": "import", "parent": null, "start_line": 36, "end_line": 36, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 12, "document": "import imports"}, "fecedfcf7dc459e4": {"name": "cli", "type": "function", "parent": null, "start_line": 42, "end_line": 44, "complexity": 1, "has_docstring": true, "content_length": 95, "token_count": 50, "document": "function cli"}, "bc6f782a4981bdb3": {"name": "analyze", "type": "function", "parent": null, "start_line": 52, "end_line": 83, "complexity": 4, "has_docstring": true, "content_length": 1142, "token_count": 451, "document": "function analyze"}, "d919a80cde1460b8": {"name": "search", "type": "function", "parent": null, "start_line": 91, "end_line": 121, "complexity": 3, "has_docstring": true, "content_length": 1009, "token_count": 382, "document": "function search"}, "6972e8a783a181f7": {"name": "list_chunks", "type": "function", "parent": null, "start_line": 128, "end_line": 157, "complexity": 6, "has_docstring": true, "content_length": 906, "token_count": 395, "document": "function list_chunks"}, "17efffc0d285409d": {"name": "stats", "type": "function", "parent": null, "start_line": 161, "end_line": 186, "complexity": 3, "has_docstring": true, "content_length": 951, "token_count": 354, "document": "function stats"}, "9ff83ec15cecedf0": {"name": "reset", "type": "function", "parent": null, "start_line": 191, "end_line": 209, "complexity": 3, "has_docstring": true, "content_length": 585, "token_count": 229, "document": "function reset"}, "fb5ef9115127cbc6": {"name": "generate", "type": "function", "parent": null, "start_line": 216, "end_line": 233, "complexity": 2, "has_docstring": true, "content_length": 642, "token_count": 236, "document": "function generate"}, "dabfebf374863c43": {"name": "test", "type": "function", "parent": null, "start_line": 242, "end_line": 256, "complexity": 3, "has_docstring": true, "content_length": 544, "token_count": 222, "document": "function test"}, "37ba3a0be28e2e0f": {"name": "display_analysis_results", "type": "function", "parent": null, "start_line": 262, "end_line": 285, "complexity": 3, "has_docstring": true, "content_length": 1068, "token_count": 410, "document": "function display_analysis_results"}, "62cd92a514f6bb48": {"name": "display_search_results", "type": "function", "parent": null, "start_line": 288, "end_line": 314, "complexity": 3, "has_docstring": true, "content_length": 1061, "token_count": 421, "document": "function display_search_results"}, "f3e25aeaf9ad4eac": {"name": "display_chunk_list", "type": "function", "parent": null, "start_line": 317, "end_line": 345, "complexity": 3, "has_docstring": true, "content_length": 1148, "token_count": 453, "document": "function display_chunk_list"}, "d15eb913c2611b7e": {"name": "augment", "type": "function", "parent": null, "start_line": 358, "end_line": 427, "complexity": 7, "has_docstring": true, "content_length": 2940, "token_count": 1431, "document": "function augment"}, "b4cc59eee82d84a6": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "0361e610289c70c4": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "07d894fa446d2dfc": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 17, "token_count": 9, "document": "import imports"}, "8dca49f417f773a0": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 15, "token_count": 9, "document": "import imports"}, "338fd0706dc18a75": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "e037820df12dba10": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 8, "document": "import imports"}, "679c0476fc36fe59": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 8, "document": "import imports"}, "c92baf1b545b43cc": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "15b9b8e32d4e6452": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "34eed95895661ed8": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 51, "token_count": 20, "document": "import imports"}, "9df19ad253664c08": {"name": "imports", "type": "import", "parent": null, "start_line": 11, "end_line": 11, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 14, "document": "import imports"}, "c5d4f4970bf184c2": {"name": "imports", "type": "import", "parent": null, "start_line": 12, "end_line": 12, "complexity": 0, "has_docstring": false, "content_length": 17, "token_count": 9, "document": "import imports"}, "253b314d701c6738": {"name": "imports", "type": "import", "parent": null, "start_line": 13, "end_line": 13, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "f5a6c29bb8d28138": {"name": "ExecutionResult", "type": "class", "parent": null, "start_line": 17, "end_line": 23, "complexity": 0, "has_docstring": true, "content_length": 162, "token_count": 70, "document": "class ExecutionResult"}, "6bdca1c8becc20e1": {"name": "SafeCodeExecutor", "type": "class", "parent": null, "start_line": 26, "end_line": 27, "complexity": 0, "has_docstring": true, "content_length": 79, "token_count": 39, "document": "class SafeCodeExecutor"}, "c33f5a3fc16d21d8": {"name": "__init__", "type": "method", "parent": "SafeCodeExecutor", "start_line": 29, "end_line": 40, "complexity": 1, "has_docstring": false, "content_length": 591, "token_count": 301, "document": "method __init__ in SafeCodeExecutor"}, "e2f1b8a673d52adf": {"name": "validate_code", "type": "method", "parent": "SafeCodeExecutor", "start_line": 42, "end_line": 67, "complexity": 12, "has_docstring": true, "content_length": 1111, "token_count": 547, "document": "method validate_code in SafeCodeExecutor"}, "f53b687ae4002c5f": {"name": "execute_code", "type": "method", "parent": "SafeCodeExecutor", "start_line": 69, "end_line": 188, "complexity": 5, "has_docstring": true, "content_length": 4471, "token_count": 2764, "document": "method execute_code in SafeCodeExecutor"}, "934637e932552c31": {"name": "execute_file", "type": "method", "parent": "SafeCodeExecutor", "start_line": 190, "end_line": 202, "complexity": 3, "has_docstring": true, "content_length": 461, "token_count": 269, "document": "method execute_file in SafeCodeExecutor"}, "43059ebed0d3d259": {"name": "test_function", "type": "method", "parent": "SafeCodeExecutor", "start_line": 204, "end_line": 234, "complexity": 3, "has_docstring": true, "content_length": 1070, "token_count": 507, "document": "method test_function in SafeCodeExecutor"}, "a62a84b43bbb2e75": {"name": "CodeTester", "type": "class", "parent": null, "start_line": 237, "end_line": 238, "complexity": 0, "has_docstring": true, "content_length": 62, "token_count": 31, "document": "class CodeTester"}, "9d1282f6dc7417ce": {"name": "__init__", "type": "method", "parent": "CodeTester", "start_line": 240, "end_line": 241, "complexity": 1, "has_docstring": false, "content_length": 66, "token_count": 42, "document": "method __init__ in CodeTester"}, "38357355cae072ff": {"name": "generate_tests", "type": "method", "parent": "CodeTester", "start_line": 243, "end_line": 273, "complexity": 7, "has_docstring": true, "content_length": 1340, "token_count": 770, "document": "method generate_tests in CodeTester"}, "d1312495257517cf": {"name": "test_code", "type": "method", "parent": "CodeTester", "start_line": 275, "end_line": 303, "complexity": 3, "has_docstring": true, "content_length": 1130, "token_count": 571, "document": "method test_code in CodeTester"}, "2569ad7465de72c2": {"name": "validate_generated_code", "type": "method", "parent": "CodeTester", "start_line": 305, "end_line": 342, "complexity": 9, "has_docstring": true, "content_length": 1381, "token_count": 667, "document": "method validate_generated_code in CodeTester"}, "22cf0a8798ab5713": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "688725f1ac48dd68": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 9, "document": "import imports"}, "5ebf380f51c24418": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "2487f117f0107a83": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 38, "token_count": 15, "document": "import imports"}, "d0b49a4fabae240b": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 49, "token_count": 17, "document": "import imports"}, "47c0d2e6b53e1e95": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 64, "token_count": 30, "document": "import imports"}, "0a55b16638709cfb": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 12, "document": "import imports"}, "df3f2aec1b1444d3": {"name": "PathEncryption", "type": "class", "parent": null, "start_line": 12, "end_line": 13, "complexity": 0, "has_docstring": true, "content_length": 103, "token_count": 47, "document": "class PathEncryption"}, "1d270823d5bda52d": {"name": "__init__", "type": "method", "parent": "PathEncryption", "start_line": 15, "end_line": 17, "complexity": 1, "has_docstring": false, "content_length": 157, "token_count": 86, "document": "method __init__ in PathEncryption"}, "a47c08558299cff3": {"name": "_get_or_create_cipher", "type": "method", "parent": "PathEncryption", "start_line": 19, "end_line": 31, "complexity": 4, "has_docstring": true, "content_length": 478, "token_count": 289, "document": "method _get_or_create_cipher in PathEncryption"}, "edfe0ce974a01432": {"name": "encrypt_path", "type": "method", "parent": "PathEncryption", "start_line": 33, "end_line": 37, "complexity": 1, "has_docstring": true, "content_length": 256, "token_count": 133, "document": "method encrypt_path in PathEncryption"}, "9a88b0da1117324d": {"name": "decrypt_path", "type": "method", "parent": "PathEncryption", "start_line": 39, "end_line": 43, "complexity": 1, "has_docstring": true, "content_length": 271, "token_count": 134, "document": "method decrypt_path in PathEncryption"}, "a655416a49c35720": {"name": "hash_content", "type": "method", "parent": "PathEncryption", "start_line": 45, "end_line": 49, "complexity": 1, "has_docstring": true, "content_length": 236, "token_count": 117, "document": "method hash_content in PathEncryption"}, "c5e806b389e8c4d7": {"name": "generate_chunk_id", "type": "function", "parent": null, "start_line": 52, "end_line": 57, "complexity": 1, "has_docstring": true, "content_length": 398, "token_count": 174, "document": "function generate_chunk_id"}, "4f171df36c1b12ce": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 8, "document": "import imports"}, "e0c5b9112eac20f1": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "cc0700391993b667": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 44, "token_count": 17, "document": "import imports"}, "cbcbd81624cb1f3e": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "f1227d0e54daeed2": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 12, "document": "import imports"}, "87adb8f15b70a854": {"name": "CodeChunk", "type": "class", "parent": null, "start_line": 12, "end_line": 21, "complexity": 0, "has_docstring": true, "content_length": 343, "token_count": 140, "document": "class CodeChunk"}, "34ed1daf37022a8c": {"name": "PythonParser", "type": "class", "parent": null, "start_line": 24, "end_line": 25, "complexity": 0, "has_docstring": true, "content_length": 63, "token_count": 34, "document": "class PythonParser"}, "95fab35d9962d522": {"name": "__init__", "type": "method", "parent": "PythonParser", "start_line": 27, "end_line": 28, "complexity": 1, "has_docstring": false, "content_length": 65, "token_count": 44, "document": "method __init__ in PythonParser"}, "1311850e709f71f3": {"name": "_extract_chunks", "type": "method", "parent": "PythonParser", "start_line": 48, "end_line": 60, "complexity": 5, "has_docstring": true, "content_length": 655, "token_count": 313, "document": "method _extract_chunks in PythonParser"}, "85ea49ffd13b1faf": {"name": "_process_function", "type": "method", "parent": "PythonParser", "start_line": 63, "end_line": 88, "complexity": 2, "has_docstring": true, "content_length": 906, "token_count": 422, "document": "method _process_function in PythonParser"}, "744bb1fc9bfe60ed": {"name": "_process_class", "type": "method", "parent": "PythonParser", "start_line": 90, "end_line": 119, "complexity": 6, "has_docstring": true, "content_length": 1102, "token_count": 524, "document": "method _process_class in PythonParser"}, "5bcfcd124ed69054": {"name": "_process_import", "type": "method", "parent": "PythonParser", "start_line": 121, "end_line": 136, "complexity": 1, "has_docstring": true, "content_length": 483, "token_count": 261, "document": "method _process_import in PythonParser"}, "448fca84e67ad886": {"name": "_calculate_complexity", "type": "method", "parent": "PythonParser", "start_line": 138, "end_line": 148, "complexity": 4, "has_docstring": true, "content_length": 449, "token_count": 224, "document": "method _calculate_complexity in PythonParser"}, "4e11e15c6168dac1": {"name": "UniversalParser", "type": "class", "parent": null, "start_line": 151, "end_line": 152, "complexity": 0, "has_docstring": true, "content_length": 87, "token_count": 36, "document": "class UniversalParser"}, "53230e2bf15d8730": {"name": "__init__", "type": "method", "parent": "UniversalParser", "start_line": 154, "end_line": 157, "complexity": 1, "has_docstring": false, "content_length": 92, "token_count": 65, "document": "method __init__ in UniversalParser"}, "81afb58a1876a1d3": {"name": "parse_file", "type": "method", "parent": "UniversalParser", "start_line": 159, "end_line": 167, "complexity": 2, "has_docstring": true, "content_length": 385, "token_count": 191, "document": "method parse_file in UniversalParser"}, "1c433623523891a5": {"name": "_parse_as_text", "type": "method", "parent": "UniversalParser", "start_line": 169, "end_line": 193, "complexity": 3, "has_docstring": true, "content_length": 804, "token_count": 440, "document": "method _parse_as_text in UniversalParser"}, "1e8a292482fb2b6a": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "0ca01983e4afcb90": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "79dadd72dd40603d": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 17, "token_count": 9, "document": "import imports"}, "f290efca48ece30d": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "873a2daddf57b44b": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 51, "token_count": 20, "document": "import imports"}, "a4e458b175f32e0d": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 14, "document": "import imports"}, "71c36307e1051763": {"name": "imports", "type": "import", "parent": null, "start_line": 11, "end_line": 11, "complexity": 0, "has_docstring": false, "content_length": 32, "token_count": 12, "document": "import imports"}, "ba00dc5803c7732b": {"name": "imports", "type": "import", "parent": null, "start_line": 12, "end_line": 12, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 12, "document": "import imports"}, "8252e406e3aac0af": {"name": "imports", "type": "import", "parent": null, "start_line": 13, "end_line": 13, "complexity": 0, "has_docstring": false, "content_length": 30, "token_count": 14, "document": "import imports"}, "bf1ad2db15b23dec": {"name": "imports", "type": "import", "parent": null, "start_line": 14, "end_line": 14, "complexity": 0, "has_docstring": false, "content_length": 61, "token_count": 19, "document": "import imports"}, "70086379c86e5390": {"name": "imports", "type": "import", "parent": null, "start_line": 16, "end_line": 16, "complexity": 0, "has_docstring": false, "content_length": 43, "token_count": 18, "document": "import imports"}, "952ec084b048797b": {"name": "imports", "type": "import", "parent": null, "start_line": 17, "end_line": 17, "complexity": 0, "has_docstring": false, "content_length": 64, "token_count": 21, "document": "import imports"}, "7cb268447b0a396c": {"name": "imports", "type": "import", "parent": null, "start_line": 18, "end_line": 18, "complexity": 0, "has_docstring": false, "content_length": 42, "token_count": 16, "document": "import imports"}, "f2f96bd4f5390a06": {"name": "IterationResult", "type": "class", "parent": null, "start_line": 22, "end_line": 30, "complexity": 0, "has_docstring": true, "content_length": 257, "token_count": 101, "document": "class IterationResult"}, "241c422b0fcd8360": {"name": "AugmentSession", "type": "class", "parent": null, "start_line": 34, "end_line": 42, "complexity": 0, "has_docstring": true, "content_length": 260, "token_count": 107, "document": "class AugmentSession"}, "5b6f39aa720434f5": {"name": "AugmentEngine", "type": "class", "parent": null, "start_line": 45, "end_line": 46, "complexity": 0, "has_docstring": true, "content_length": 90, "token_count": 49, "document": "class AugmentEngine"}, "93b0551e0b0ef882": {"name": "__init__", "type": "method", "parent": "AugmentEngine", "start_line": 48, "end_line": 54, "complexity": 1, "has_docstring": false, "content_length": 276, "token_count": 131, "document": "method __init__ in AugmentEngine"}, "b4256cacda961697": {"name": "augment_code", "type": "method", "parent": "AugmentEngine", "start_line": 56, "end_line": 156, "complexity": 8, "has_docstring": true, "content_length": 3921, "token_count": 1900, "document": "method augment_code in AugmentEngine"}, "3d7610c6e5ccc188": {"name": "_understand_codebase", "type": "method", "parent": "AugmentEngine", "start_line": 158, "end_line": 196, "complexity": 5, "has_docstring": true, "content_length": 1513, "token_count": 679, "document": "method _understand_codebase in AugmentEngine"}, "f7e97d98cf831561": {"name": "_generate_initial_code", "type": "method", "parent": "AugmentEngine", "start_line": 198, "end_line": 227, "complexity": 3, "has_docstring": true, "content_length": 1056, "token_count": 390, "document": "method _generate_initial_code in AugmentEngine"}, "c6473f52837b714d": {"name": "_execute_in_terminal", "type": "method", "parent": "AugmentEngine", "start_line": 229, "end_line": 299, "complexity": 7, "has_docstring": true, "content_length": 2779, "token_count": 1361, "document": "method _execute_in_terminal in AugmentEngine"}, "c121605f55d3b8e8": {"name": "_has_runtime_errors", "type": "method", "parent": "AugmentEngine", "start_line": 301, "end_line": 309, "complexity": 1, "has_docstring": true, "content_length": 447, "token_count": 202, "document": "method _has_runtime_errors in AugmentEngine"}, "d647227cb2fe9ef6": {"name": "_analyze_errors", "type": "method", "parent": "AugmentEngine", "start_line": 311, "end_line": 375, "complexity": 11, "has_docstring": true, "content_length": 2925, "token_count": 954, "document": "method _analyze_errors in AugmentEngine"}, "925fda49b8d45d74": {"name": "_correct_code", "type": "method", "parent": "AugmentEngine", "start_line": 377, "end_line": 388, "complexity": 4, "has_docstring": true, "content_length": 768, "token_count": 313, "document": "method _correct_code in AugmentEngine"}, "265ca2880292994f": {"name": "_fix_import_errors", "type": "method", "parent": "AugmentEngine", "start_line": 390, "end_line": 444, "complexity": 8, "has_docstring": true, "content_length": 2325, "token_count": 971, "document": "method _fix_import_errors in AugmentEngine"}, "49459a86e4353d41": {"name": "_fix_pandas_errors", "type": "method", "parent": "AugmentEngine", "start_line": 446, "end_line": 455, "complexity": 1, "has_docstring": true, "content_length": 564, "token_count": 253, "document": "method _fix_pandas_errors in AugmentEngine"}, "4a631e70a9785883": {"name": "_general_correction", "type": "method", "parent": "AugmentEngine", "start_line": 457, "end_line": 502, "complexity": 2, "has_docstring": true, "content_length": 1589, "token_count": 566, "document": "method _general_correction in AugmentEngine"}, "d7305f51e7fe13e7": {"name": "_analyze_code_structure", "type": "method", "parent": "AugmentEngine", "start_line": 504, "end_line": 546, "complexity": 8, "has_docstring": true, "content_length": 1627, "token_count": 848, "document": "method _analyze_code_structure in AugmentEngine"}, "9ed69bfdc2e010b0": {"name": "_analyze_error_type", "type": "method", "parent": "AugmentEngine", "start_line": 548, "end_line": 579, "complexity": 9, "has_docstring": true, "content_length": 1581, "token_count": 648, "document": "method _analyze_error_type in AugmentEngine"}, "a9433f6798f9b0b3": {"name": "_apply_smart_fixes", "type": "method", "parent": "AugmentEngine", "start_line": 581, "end_line": 611, "complexity": 13, "has_docstring": true, "content_length": 1382, "token_count": 643, "document": "method _apply_smart_fixes in AugmentEngine"}, "7ad475169bb99112": {"name": "_clean_generated_code", "type": "method", "parent": "AugmentEngine", "start_line": 613, "end_line": 627, "complexity": 4, "has_docstring": true, "content_length": 492, "token_count": 257, "document": "method _clean_generated_code in AugmentEngine"}, "ef9d367b37d63725": {"name": "_display_iteration_result", "type": "method", "parent": "AugmentEngine", "start_line": 629, "end_line": 648, "complexity": 3, "has_docstring": true, "content_length": 1108, "token_count": 447, "document": "method _display_iteration_result in AugmentEngine"}, "26da49c21637a1ea": {"name": "_display_session_summary", "type": "method", "parent": "AugmentEngine", "start_line": 650, "end_line": 674, "complexity": 3, "has_docstring": true, "content_length": 1027, "token_count": 413, "document": "method _display_session_summary in AugmentEngine"}, "af63d28d4fd2b757": {"name": "save_final_code", "type": "method", "parent": "AugmentEngine", "start_line": 676, "end_line": 692, "complexity": 5, "has_docstring": true, "content_length": 776, "token_count": 349, "document": "method save_final_code in AugmentEngine"}, "618230b4f6db9ace": {"name": "get_session_report", "type": "method", "parent": "AugmentEngine", "start_line": 694, "end_line": 725, "complexity": 3, "has_docstring": true, "content_length": 903, "token_count": 358, "document": "method get_session_report in AugmentEngine"}, "2ee043cebd44ab1b": {"name": "_extract_required_packages", "type": "method", "parent": "AugmentEngine", "start_line": 727, "end_line": 772, "complexity": 4, "has_docstring": true, "content_length": 1571, "token_count": 843, "document": "method _extract_required_packages in AugmentEngine"}, "193391ee7e17b159": {"name": "_install_packages", "type": "method", "parent": "AugmentEngine", "start_line": 774, "end_line": 791, "complexity": 4, "has_docstring": true, "content_length": 792, "token_count": 432, "document": "method _install_packages in AugmentEngine"}, "c18b7843c68e28e1": {"name": "_get_enhanced_env", "type": "method", "parent": "AugmentEngine", "start_line": 793, "end_line": 802, "complexity": 2, "has_docstring": true, "content_length": 312, "token_count": 162, "document": "method _get_enhanced_env in AugmentEngine"}, "e65caad521bcf4e3": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 11, "document": "import imports"}, "546b2a18148bdf9c": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 39, "token_count": 15, "document": "import imports"}, "1d5ce82163408bba": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 15, "token_count": 10, "document": "import imports"}, "083bcf4d3e48beb0": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 14, "document": "import imports"}, "89957deeb3795056": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 12, "token_count": 8, "document": "import imports"}, "637c420edece3a38": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 49, "token_count": 16, "document": "import imports"}, "61e4da6b55e4800e": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 12, "document": "import imports"}, "8f3d1e2881e965a2": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 38, "token_count": 16, "document": "import imports"}, "41e28665a106db62": {"name": "CodeEmbedding", "type": "class", "parent": null, "start_line": 14, "end_line": 19, "complexity": 0, "has_docstring": true, "content_length": 163, "token_count": 82, "document": "class CodeEmbedding"}, "3508e96cd15306a8": {"name": "EmbeddingGenerator", "type": "class", "parent": null, "start_line": 22, "end_line": 23, "complexity": 0, "has_docstring": true, "content_length": 82, "token_count": 45, "document": "class EmbeddingGenerator"}, "168613597af6e172": {"name": "__init__", "type": "method", "parent": "EmbeddingGenerator", "start_line": 25, "end_line": 29, "complexity": 1, "has_docstring": false, "content_length": 186, "token_count": 101, "document": "method __init__ in EmbeddingGenerator"}, "927371ec31ff6c7c": {"name": "_load_model", "type": "method", "parent": "EmbeddingGenerator", "start_line": 31, "end_line": 42, "complexity": 2, "has_docstring": true, "content_length": 547, "token_count": 280, "document": "method _load_model in EmbeddingGenerator"}, "78779222324015b5": {"name": "_transformer_embedding", "type": "method", "parent": "EmbeddingGenerator", "start_line": 45, "end_line": 60, "complexity": 2, "has_docstring": true, "content_length": 837, "token_count": 390, "document": "method _transformer_embedding in EmbeddingGenerator"}, "f4c8b96303dcfa6c": {"name": "_simple_embedding", "type": "method", "parent": "EmbeddingGenerator", "start_line": 62, "end_line": 75, "complexity": 3, "has_docstring": true, "content_length": 666, "token_count": 341, "document": "method _simple_embedding in EmbeddingGenerator"}, "b62e199fad1c11ec": {"name": "_prepare_text", "type": "method", "parent": "EmbeddingGenerator", "start_line": 77, "end_line": 87, "complexity": 5, "has_docstring": true, "content_length": 487, "token_count": 256, "document": "method _prepare_text in EmbeddingGenerator"}, "6073cd62e5416a72": {"name": "generate_embedding", "type": "method", "parent": "EmbeddingGenerator", "start_line": 89, "end_line": 120, "complexity": 4, "has_docstring": true, "content_length": 1251, "token_count": 684, "document": "method generate_embedding in EmbeddingGenerator"}, "3420e1b072545541": {"name": "batch_generate_embeddings", "type": "method", "parent": "EmbeddingGenerator", "start_line": 122, "end_line": 129, "complexity": 3, "has_docstring": true, "content_length": 394, "token_count": 204, "document": "method batch_generate_embeddings in EmbeddingGenerator"}, "f1de53deab43ee9b": {"name": "calculate_similarity", "type": "method", "parent": "EmbeddingGenerator", "start_line": 131, "end_line": 142, "complexity": 4, "has_docstring": true, "content_length": 554, "token_count": 296, "document": "method calculate_similarity in EmbeddingGenerator"}, "fb2a15b9ba09598a": {"name": "find_similar_chunks", "type": "method", "parent": "EmbeddingGenerator", "start_line": 144, "end_line": 153, "complexity": 2, "has_docstring": true, "content_length": 573, "token_count": 289, "document": "method find_similar_chunks in EmbeddingGenerator"}, "8e18ce0115cb2dba": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 14, "token_count": 9, "document": "import imports"}, "0de000267c30984d": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 44, "token_count": 17, "document": "import imports"}, "302d53694ec25c1c": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 41, "token_count": 17, "document": "import imports"}, "266e087a2a5241e1": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "3f7988bd363f09e3": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "950cc087167f137b": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "9a0a30757f7a2a67": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 38, "token_count": 16, "document": "import imports"}, "2481bcef9e03fb0f": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 57, "token_count": 22, "document": "import imports"}, "30fe4a39831871f6": {"name": "ChunkFingerprint", "type": "class", "parent": null, "start_line": 14, "end_line": 25, "complexity": 0, "has_docstring": true, "content_length": 280, "token_count": 131, "document": "class ChunkFingerprint"}, "27776ca8b570804f": {"name": "MerkleNode", "type": "class", "parent": null, "start_line": 28, "end_line": 29, "complexity": 0, "has_docstring": true, "content_length": 84, "token_count": 47, "document": "class MerkleNode"}, "646d36c1417a91f3": {"name": "__init__", "type": "method", "parent": "MerkleNode", "start_line": 31, "end_line": 36, "complexity": 1, "has_docstring": false, "content_length": 256, "token_count": 135, "document": "method __init__ in MerkleNode"}, "f96bbbdd5567a77f": {"name": "add_child", "type": "method", "parent": "MerkleNode", "start_line": 38, "end_line": 40, "complexity": 1, "has_docstring": true, "content_length": 122, "token_count": 76, "document": "method add_child in MerkleNode"}, "c414c61a1203bcbd": {"name": "add_chunk_hash", "type": "method", "parent": "MerkleNode", "start_line": 42, "end_line": 44, "complexity": 1, "has_docstring": true, "content_length": 136, "token_count": 88, "document": "method add_chunk_hash in MerkleNode"}, "c8079ef9037e1930": {"name": "calculate_hash", "type": "method", "parent": "MerkleNode", "start_line": 46, "end_line": 61, "complexity": 2, "has_docstring": true, "content_length": 591, "token_count": 286, "document": "method calculate_hash in MerkleNode"}, "3afa2d2b8cdd2632": {"name": "FingerprintManager", "type": "class", "parent": null, "start_line": 64, "end_line": 65, "complexity": 0, "has_docstring": true, "content_length": 95, "token_count": 43, "document": "class FingerprintManager"}, "e2ff8c555f1f606d": {"name": "__init__", "type": "method", "parent": "FingerprintManager", "start_line": 67, "end_line": 72, "complexity": 1, "has_docstring": false, "content_length": 313, "token_count": 142, "document": "method __init__ in FingerprintManager"}, "32bf5b0ea93ec874": {"name": "load_fingerprints", "type": "method", "parent": "FingerprintManager", "start_line": 74, "end_line": 87, "complexity": 5, "has_docstring": true, "content_length": 574, "token_count": 311, "document": "method load_fingerprints in FingerprintManager"}, "00dcb13ff86bcc2b": {"name": "save_fingerprints", "type": "method", "parent": "FingerprintManager", "start_line": 89, "end_line": 100, "complexity": 2, "has_docstring": true, "content_length": 456, "token_count": 251, "document": "method save_fingerprints in FingerprintManager"}, "78d2b276e2a7c8a1": {"name": "create_fingerprint", "type": "method", "parent": "FingerprintManager", "start_line": 102, "end_line": 124, "complexity": 1, "has_docstring": false, "content_length": 858, "token_count": 442, "document": "method create_fingerprint in FingerprintManager"}, "b0dac7c45abdb63b": {"name": "build_merkle_tree", "type": "method", "parent": "FingerprintManager", "start_line": 169, "end_line": 191, "complexity": 5, "has_docstring": true, "content_length": 907, "token_count": 452, "document": "method build_merkle_tree in FingerprintManager"}, "db6a0bb20bfdff56": {"name": "_add_file_to_tree", "type": "method", "parent": "FingerprintManager", "start_line": 193, "end_line": 220, "complexity": 6, "has_docstring": true, "content_length": 1122, "token_count": 565, "document": "method _add_file_to_tree in FingerprintManager"}, "075b7903de20140f": {"name": "detect_changes", "type": "method", "parent": "FingerprintManager", "start_line": 222, "end_line": 228, "complexity": 2, "has_docstring": true, "content_length": 303, "token_count": 153, "document": "method detect_changes in FingerprintManager"}, "f5c327d42bfb29d1": {"name": "get_file_changes", "type": "method", "parent": "FingerprintManager", "start_line": 230, "end_line": 248, "complexity": 4, "has_docstring": true, "content_length": 741, "token_count": 388, "document": "method get_file_changes in FingerprintManager"}, "6b37355fe29c93bd": {"name": "main", "type": "function", "parent": null, "start_line": 1, "end_line": 4, "complexity": 1, "has_docstring": false, "content_length": 154, "token_count": 53, "document": "function main"}, "f9bc39784a5b1159": {"name": "add", "type": "function", "parent": null, "start_line": 5, "end_line": 7, "complexity": 1, "has_docstring": true, "content_length": 58, "token_count": 37, "document": "function add"}, "e59bab70ba6b5925": {"name": "subtract", "type": "function", "parent": null, "start_line": 9, "end_line": 11, "complexity": 1, "has_docstring": true, "content_length": 65, "token_count": 43, "document": "function subtract"}, "ebd997d97888afbf": {"name": "divide", "type": "function", "parent": null, "start_line": 17, "end_line": 25, "complexity": 2, "has_docstring": true, "content_length": 196, "token_count": 104, "document": "function divide"}, "31b266095acd843b": {"name": "modulo", "type": "function", "parent": null, "start_line": 27, "end_line": 35, "complexity": 2, "has_docstring": true, "content_length": 222, "token_count": 113, "document": "function modulo"}, "4765c33a1e508548": {"name": "power", "type": "function", "parent": null, "start_line": 37, "end_line": 45, "complexity": 2, "has_docstring": true, "content_length": 193, "token_count": 105, "document": "function power"}, "efd69f006f5f70b7": {"name": "main", "type": "function", "parent": null, "start_line": 47, "end_line": 85, "complexity": 11, "has_docstring": true, "content_length": 1291, "token_count": 698, "document": "function main"}, "358a42ccfd170dd0": {"name": "validate_number", "type": "function", "parent": null, "start_line": 6, "end_line": 12, "complexity": 2, "has_docstring": true, "content_length": 164, "token_count": 87, "document": "function validate_number"}, "45c23f5ae6e0493b": {"name": "format_result", "type": "function", "parent": null, "start_line": 14, "end_line": 18, "complexity": 2, "has_docstring": true, "content_length": 154, "token_count": 75, "document": "function format_result"}, "ac44d77867a379f6": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 11, "document": "import imports"}, "7d072bc9ad4bb921": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 44, "token_count": 17, "document": "import imports"}, "1c7a172886a44d1b": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "c763a5366ed21b62": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "af5478587f9b36e1": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 9, "document": "import imports"}, "ed485b554fa2b8aa": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 12, "document": "import imports"}, "d8e501fb0d1f932c": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 41, "token_count": 17, "document": "import imports"}, "c94b10d3d44d43c3": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 38, "token_count": 15, "document": "import imports"}, "377f230daa2a74d9": {"name": "VectorDatabase", "type": "class", "parent": null, "start_line": 13, "end_line": 14, "complexity": 0, "has_docstring": true, "content_length": 107, "token_count": 49, "document": "class VectorDatabase"}, "9ceceb0de03c2b12": {"name": "__init__", "type": "method", "parent": "VectorDatabase", "start_line": 16, "end_line": 28, "complexity": 1, "has_docstring": false, "content_length": 474, "token_count": 227, "document": "method __init__ in VectorDatabase"}, "cde12151c8900011": {"name": "_load_embeddings", "type": "method", "parent": "VectorDatabase", "start_line": 30, "end_line": 38, "complexity": 4, "has_docstring": true, "content_length": 367, "token_count": 217, "document": "method _load_embeddings in VectorDatabase"}, "4fd7fd1def019cc8": {"name": "_load_vectors", "type": "method", "parent": "VectorDatabase", "start_line": 40, "end_line": 48, "complexity": 4, "has_docstring": true, "content_length": 356, "token_count": 210, "document": "method _load_vectors in VectorDatabase"}, "0cc052c24d92cfc7": {"name": "_save_embeddings", "type": "method", "parent": "VectorDatabase", "start_line": 50, "end_line": 56, "complexity": 3, "has_docstring": true, "content_length": 290, "token_count": 169, "document": "method _save_embeddings in VectorDatabase"}, "c088b5c905515105": {"name": "_save_vectors", "type": "method", "parent": "VectorDatabase", "start_line": 58, "end_line": 64, "complexity": 3, "has_docstring": true, "content_length": 263, "token_count": 156, "document": "method _save_vectors in VectorDatabase"}, "a1d4a321da70756b": {"name": "store_embeddings", "type": "method", "parent": "VectorDatabase", "start_line": 66, "end_line": 106, "complexity": 5, "has_docstring": true, "content_length": 1667, "token_count": 876, "document": "method store_embeddings in VectorDatabase"}, "6a119732bdd27d21": {"name": "search_similar", "type": "method", "parent": "VectorDatabase", "start_line": 108, "end_line": 145, "complexity": 7, "has_docstring": true, "content_length": 1483, "token_count": 801, "document": "method search_similar in VectorDatabase"}, "c610209be53f4809": {"name": "_cosine_similarity", "type": "method", "parent": "VectorDatabase", "start_line": 147, "end_line": 159, "complexity": 4, "has_docstring": true, "content_length": 456, "token_count": 263, "document": "method _cosine_similarity in VectorDatabase"}, "8e7493bf6c63e030": {"name": "search_by_text", "type": "method", "parent": "VectorDatabase", "start_line": 161, "end_line": 205, "complexity": 12, "has_docstring": true, "content_length": 1625, "token_count": 902, "document": "method search_by_text in VectorDatabase"}, "3a763802b392aab0": {"name": "filter_by_metadata", "type": "method", "parent": "VectorDatabase", "start_line": 207, "end_line": 231, "complexity": 6, "has_docstring": true, "content_length": 867, "token_count": 499, "document": "method filter_by_metadata in VectorDatabase"}, "992fbe777a1b9a78": {"name": "delete_chunks", "type": "method", "parent": "VectorDatabase", "start_line": 253, "end_line": 271, "complexity": 5, "has_docstring": true, "content_length": 672, "token_count": 366, "document": "method delete_chunks in VectorDatabase"}, "a78973651c992faa": {"name": "get_statistics", "type": "method", "parent": "VectorDatabase", "start_line": 273, "end_line": 305, "complexity": 4, "has_docstring": true, "content_length": 1226, "token_count": 657, "document": "method get_statistics in VectorDatabase"}, "62b4ef4a780ce665": {"name": "reset_database", "type": "method", "parent": "VectorDatabase", "start_line": 307, "end_line": 325, "complexity": 4, "has_docstring": true, "content_length": 601, "token_count": 323, "document": "method reset_database in VectorDatabase"}, "ea782de0e4a1de75": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "092f2d065065e179": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 15, "token_count": 8, "document": "import imports"}, "44b4901593eebe65": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 44, "token_count": 17, "document": "import imports"}, "357acad8093d1532": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 14, "document": "import imports"}, "e829462b66d3d9c4": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "322897ddc754eb6f": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 12, "document": "import imports"}, "97a409bc6b4f9636": {"name": "CodeChange", "type": "class", "parent": null, "start_line": 27, "end_line": 34, "complexity": 0, "has_docstring": true, "content_length": 246, "token_count": 109, "document": "class CodeChange"}, "83e7f5f3311da282": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "class", "parent": null, "start_line": 37, "end_line": 38, "complexity": 0, "has_docstring": true, "content_length": 58, "token_count": 35, "document": "class LLMProvider"}, "1d9607a3adf74eca": {"name": "generate_code", "type": "method", "parent": "<PERSON><PERSON><PERSON><PERSON>", "start_line": 40, "end_line": 42, "complexity": 1, "has_docstring": true, "content_length": 158, "token_count": 81, "document": "method generate_code in LLMProvider"}, "b9edd6ad5135d952": {"name": "analyze_code", "type": "method", "parent": "<PERSON><PERSON><PERSON><PERSON>", "start_line": 44, "end_line": 46, "complexity": 1, "has_docstring": true, "content_length": 152, "token_count": 79, "document": "method analyze_code in LLMProvider"}, "632d571dab27494d": {"name": "OpenAIProvider", "type": "class", "parent": null, "start_line": 49, "end_line": 50, "complexity": 0, "has_docstring": true, "content_length": 68, "token_count": 37, "document": "class OpenAIProvider"}, "ef5d34f3ebb655c9": {"name": "__init__", "type": "method", "parent": "OpenAIProvider", "start_line": 52, "end_line": 60, "complexity": 3, "has_docstring": false, "content_length": 365, "token_count": 192, "document": "method __init__ in OpenAIProvider"}, "f7ba780be9ef4108": {"name": "generate_code", "type": "method", "parent": "OpenAIProvider", "start_line": 62, "end_line": 123, "complexity": 5, "has_docstring": true, "content_length": 2724, "token_count": 1174, "document": "method generate_code in OpenAIProvider"}, "46a1cb36d22e89f2": {"name": "analyze_code", "type": "method", "parent": "OpenAIProvider", "start_line": 125, "end_line": 171, "complexity": 4, "has_docstring": true, "content_length": 1439, "token_count": 767, "document": "method analyze_code in OpenAIProvider"}, "680bc993aaa7d43f": {"name": "_fallback_response", "type": "method", "parent": "OpenAIProvider", "start_line": 173, "end_line": 289, "complexity": 7, "has_docstring": true, "content_length": 3732, "token_count": 1632, "document": "method _fallback_response in OpenAIProvider"}, "760f9b171570170c": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "class", "parent": null, "start_line": 292, "end_line": 293, "complexity": 0, "has_docstring": true, "content_length": 96, "token_count": 49, "document": "class OllamaProvider"}, "4cb01549bea8f9c9": {"name": "__init__", "type": "method", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start_line": 295, "end_line": 297, "complexity": 1, "has_docstring": false, "content_length": 151, "token_count": 79, "document": "method __init__ in OllamaProvider"}, "f8c1bbcee8f486b0": {"name": "generate_code", "type": "method", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start_line": 299, "end_line": 362, "complexity": 5, "has_docstring": true, "content_length": 2542, "token_count": 1083, "document": "method generate_code in OllamaProvider"}, "22ae319a274eadc4": {"name": "analyze_code", "type": "method", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start_line": 364, "end_line": 401, "complexity": 3, "has_docstring": true, "content_length": 1062, "token_count": 588, "document": "method analyze_code in OllamaProvider"}, "15fd4d31095eb385": {"name": "_fallback_local_response", "type": "method", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start_line": 403, "end_line": 417, "complexity": 1, "has_docstring": true, "content_length": 504, "token_count": 214, "document": "method _fallback_local_response in OllamaProvider"}, "58951450b15a2e79": {"name": "LLMManager", "type": "class", "parent": null, "start_line": 420, "end_line": 421, "complexity": 0, "has_docstring": true, "content_length": 70, "token_count": 39, "document": "class LLMManager"}, "30b753ad8d24e47b": {"name": "__init__", "type": "method", "parent": "LLMManager", "start_line": 423, "end_line": 428, "complexity": 1, "has_docstring": false, "content_length": 181, "token_count": 108, "document": "method __init__ in LLMManager"}, "06b9b34dbd04954e": {"name": "set_provider", "type": "method", "parent": "LLMManager", "start_line": 430, "end_line": 435, "complexity": 2, "has_docstring": true, "content_length": 265, "token_count": 136, "document": "method set_provider in LLMManager"}, "e07bae814f7b85f1": {"name": "generate_code", "type": "method", "parent": "LLMManager", "start_line": 437, "end_line": 441, "complexity": 2, "has_docstring": true, "content_length": 328, "token_count": 140, "document": "method generate_code in LLMManager"}, "e289a28539ef62ef": {"name": "analyze_code", "type": "method", "parent": "LLMManager", "start_line": 443, "end_line": 447, "complexity": 2, "has_docstring": true, "content_length": 318, "token_count": 137, "document": "method analyze_code in LLMManager"}, "863af7029eef3b90": {"name": "plan_changes", "type": "method", "parent": "LLMManager", "start_line": 449, "end_line": 496, "complexity": 11, "has_docstring": true, "content_length": 1987, "token_count": 900, "document": "method plan_changes in LLMManager"}, "56f27d3eb97246e7": {"name": "validate_number", "type": "function", "parent": null, "start_line": 5, "end_line": 11, "complexity": 2, "has_docstring": true, "content_length": 164, "token_count": 87, "document": "function validate_number"}, "897c73319d611358": {"name": "format_result", "type": "function", "parent": null, "start_line": 13, "end_line": 17, "complexity": 2, "has_docstring": true, "content_length": 154, "token_count": 75, "document": "function format_result"}, "d97b3a576ec1ace9": {"name": "divide", "type": "function", "parent": null, "start_line": 1, "end_line": 17, "complexity": 2, "has_docstring": true, "content_length": 448, "token_count": 250, "document": "function divide"}, "9dfb914bababdf4b": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "993dc4c2a52433f0": {"name": "divide", "type": "function", "parent": null, "start_line": 3, "end_line": 18, "complexity": 2, "has_docstring": true, "content_length": 443, "token_count": 267, "document": "function divide"}, "94a5d16c5f7b8d6d": {"name": "main", "type": "function", "parent": null, "start_line": 20, "end_line": 23, "complexity": 1, "has_docstring": false, "content_length": 117, "token_count": 54, "document": "function main"}, "6793b9fbd5f7610f": {"name": "add", "type": "function", "parent": null, "start_line": 3, "end_line": 4, "complexity": 1, "has_docstring": false, "content_length": 31, "token_count": 21, "document": "function add"}, "96ff78636a6d1df3": {"name": "subtract", "type": "function", "parent": null, "start_line": 6, "end_line": 7, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 21, "document": "function subtract"}, "f54fd0f069660098": {"name": "multiply", "type": "function", "parent": null, "start_line": 9, "end_line": 10, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 21, "document": "function multiply"}, "d4526eda501e04c7": {"name": "imports", "type": "import", "parent": null, "start_line": 11, "end_line": 11, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 8, "document": "import imports"}, "4422047ed89081f1": {"name": "imports", "type": "import", "parent": null, "start_line": 12, "end_line": 12, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "1979b188c6de9e00": {"name": "imports", "type": "import", "parent": null, "start_line": 13, "end_line": 13, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "f7db361ae5e81822": {"name": "imports", "type": "import", "parent": null, "start_line": 14, "end_line": 14, "complexity": 0, "has_docstring": false, "content_length": 17, "token_count": 9, "document": "import imports"}, "9e73637da3f16630": {"name": "imports", "type": "import", "parent": null, "start_line": 15, "end_line": 15, "complexity": 0, "has_docstring": false, "content_length": 15, "token_count": 9, "document": "import imports"}, "e8b808efcc276219": {"name": "imports", "type": "import", "parent": null, "start_line": 16, "end_line": 16, "complexity": 0, "has_docstring": false, "content_length": 24, "token_count": 11, "document": "import imports"}, "caeada0d57b81534": {"name": "imports", "type": "import", "parent": null, "start_line": 17, "end_line": 17, "complexity": 0, "has_docstring": false, "content_length": 56, "token_count": 22, "document": "import imports"}, "02d5e9c4d7b3adf2": {"name": "imports", "type": "import", "parent": null, "start_line": 18, "end_line": 18, "complexity": 0, "has_docstring": false, "content_length": 33, "token_count": 14, "document": "import imports"}, "f882b532fde7aa97": {"name": "imports", "type": "import", "parent": null, "start_line": 19, "end_line": 19, "complexity": 0, "has_docstring": false, "content_length": 32, "token_count": 12, "document": "import imports"}, "13b16c9acc326edc": {"name": "imports", "type": "import", "parent": null, "start_line": 20, "end_line": 20, "complexity": 0, "has_docstring": false, "content_length": 28, "token_count": 12, "document": "import imports"}, "301348cb002089c0": {"name": "imports", "type": "import", "parent": null, "start_line": 21, "end_line": 21, "complexity": 0, "has_docstring": false, "content_length": 30, "token_count": 14, "document": "import imports"}, "b8ae5986f191bd5c": {"name": "imports", "type": "import", "parent": null, "start_line": 22, "end_line": 22, "complexity": 0, "has_docstring": false, "content_length": 61, "token_count": 19, "document": "import imports"}, "eac17f4e8887f409": {"name": "TaskPlan", "type": "class", "parent": null, "start_line": 25, "end_line": 32, "complexity": 0, "has_docstring": true, "content_length": 398, "token_count": 140, "document": "class TaskPlan"}, "59086c4091dd78b5": {"name": "ComponentSpec", "type": "class", "parent": null, "start_line": 35, "end_line": 41, "complexity": 0, "has_docstring": true, "content_length": 207, "token_count": 84, "document": "class ComponentSpec"}, "295be1a9d7d5cadf": {"name": "ValidationResult", "type": "class", "parent": null, "start_line": 44, "end_line": 51, "complexity": 0, "has_docstring": true, "content_length": 234, "token_count": 100, "document": "class ValidationResult"}, "d0e07bf73d8ce86b": {"name": "__init__", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 56, "end_line": 60, "complexity": 1, "has_docstring": false, "content_length": 195, "token_count": 100, "document": "method __init__ in IntelligentAugmentEngine"}, "2813e384f8776b0d": {"name": "augment_code_intelligent", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 62, "end_line": 129, "complexity": 4, "has_docstring": true, "content_length": 2908, "token_count": 1369, "document": "method augment_code_intelligent in IntelligentAugmentEngine"}, "d1783b4f235b5758": {"name": "_create_task_plan", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 131, "end_line": 204, "complexity": 5, "has_docstring": true, "content_length": 3191, "token_count": 1472, "document": "method _create_task_plan in IntelligentAugmentEngine"}, "b3319f34244a2318": {"name": "_determine_implementation_order", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 206, "end_line": 226, "complexity": 6, "has_docstring": true, "content_length": 813, "token_count": 386, "document": "method _determine_implementation_order in IntelligentAugmentEngine"}, "ac28f26e18df3392": {"name": "_estimate_complexity", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 228, "end_line": 258, "complexity": 6, "has_docstring": true, "content_length": 993, "token_count": 513, "document": "method _estimate_complexity in IntelligentAugmentEngine"}, "3c338f096f771cca": {"name": "_validate_plan", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 260, "end_line": 283, "complexity": 7, "has_docstring": true, "content_length": 1062, "token_count": 459, "document": "method _validate_plan in IntelligentAugmentEngine"}, "ebc2bc25d8176900": {"name": "_get_plan_recommendations", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 285, "end_line": 298, "complexity": 4, "has_docstring": true, "content_length": 575, "token_count": 219, "document": "method _get_plan_recommendations in IntelligentAugmentEngine"}, "8837186128f19cfe": {"name": "_generate_complete_implementation", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 300, "end_line": 332, "complexity": 1, "has_docstring": true, "content_length": 1318, "token_count": 415, "document": "method _generate_complete_implementation in IntelligentAugmentEngine"}, "e8cff31259e50da9": {"name": "_generate_component", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 334, "end_line": 354, "complexity": 1, "has_docstring": true, "content_length": 807, "token_count": 274, "document": "method _generate_component in IntelligentAugmentEngine"}, "5f12cc90bacab713": {"name": "_generate_main_function", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 356, "end_line": 374, "complexity": 1, "has_docstring": true, "content_length": 754, "token_count": 257, "document": "method _generate_main_function in IntelligentAugmentEngine"}, "13562200ed669ac0": {"name": "_generate_web_scraper_component", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 376, "end_line": 393, "complexity": 1, "has_docstring": true, "content_length": 694, "token_count": 250, "document": "method _generate_web_scraper_component in IntelligentAugmentEngine"}, "7695e8582c810220": {"name": "_generate_data_analysis_component", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 395, "end_line": 412, "complexity": 1, "has_docstring": true, "content_length": 736, "token_count": 251, "document": "method _generate_data_analysis_component in IntelligentAugmentEngine"}, "1e1acedc0586b2d2": {"name": "_generate_game_component", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 414, "end_line": 431, "complexity": 1, "has_docstring": true, "content_length": 672, "token_count": 230, "document": "method _generate_game_component in IntelligentAugmentEngine"}, "13b4238b877a172b": {"name": "_generate_generic_component", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 433, "end_line": 451, "complexity": 1, "has_docstring": true, "content_length": 722, "token_count": 247, "document": "method _generate_generic_component in IntelligentAugmentEngine"}, "6c4a28d39ada96cb": {"name": "_validate_code_structure", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 453, "end_line": 527, "complexity": 25, "has_docstring": true, "content_length": 3338, "token_count": 1558, "document": "method _validate_code_structure in IntelligentAugmentEngine"}, "46e7c0290fbc3d9c": {"name": "_fix_structural_issues", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 529, "end_line": 553, "complexity": 7, "has_docstring": true, "content_length": 1179, "token_count": 495, "document": "method _fix_structural_issues in IntelligentAugmentEngine"}, "bc28c44d3481c865": {"name": "_execute_with_intelligent_handling", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 555, "end_line": 614, "complexity": 8, "has_docstring": true, "content_length": 2232, "token_count": 1265, "document": "method _execute_with_intelligent_handling in IntelligentAugmentEngine"}, "0699a063006a6db5": {"name": "_intelligent_error_fix", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 616, "end_line": 646, "complexity": 10, "has_docstring": true, "content_length": 1418, "token_count": 674, "document": "method _intelligent_error_fix in IntelligentAugmentEngine"}, "5ee5806aedcc42c4": {"name": "_add_timeout_handling", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 648, "end_line": 663, "complexity": 1, "has_docstring": true, "content_length": 422, "token_count": 188, "document": "method _add_timeout_handling in IntelligentAugmentEngine"}, "a5d2dcbdc0996161": {"name": "_create_failure_result", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 665, "end_line": 674, "complexity": 1, "has_docstring": true, "content_length": 337, "token_count": 204, "document": "method _create_failure_result in IntelligentAugmentEngine"}, "3ecb91aeb146366d": {"name": "divide", "type": "function", "parent": null, "start_line": 1, "end_line": 17, "complexity": 2, "has_docstring": true, "content_length": 448, "token_count": 250, "document": "function divide"}, "87cbc7f5f267044f": {"name": "add", "type": "function", "parent": null, "start_line": 3, "end_line": 4, "complexity": 1, "has_docstring": false, "content_length": 31, "token_count": 21, "document": "function add"}, "4dc13728f725a719": {"name": "subtract", "type": "function", "parent": null, "start_line": 6, "end_line": 7, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 21, "document": "function subtract"}, "f1e293edd32c9adb": {"name": "multiply", "type": "function", "parent": null, "start_line": 9, "end_line": 10, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 21, "document": "function multiply"}, "a353441b01f877f0": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "feddc7194c457d34": {"name": "divide", "type": "function", "parent": null, "start_line": 3, "end_line": 18, "complexity": 2, "has_docstring": true, "content_length": 443, "token_count": 267, "document": "function divide"}, "27fe28acded75199": {"name": "main", "type": "function", "parent": null, "start_line": 20, "end_line": 23, "complexity": 1, "has_docstring": false, "content_length": 117, "token_count": 54, "document": "function main"}, "cf023e988263c66c": {"name": "_generate_game_component", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 414, "end_line": 430, "complexity": 1, "has_docstring": true, "content_length": 608, "token_count": 217, "document": "method _generate_game_component in IntelligentAugmentEngine"}, "d4c8dfd1f1dc6e74": {"name": "_generate_generic_component", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 432, "end_line": 450, "complexity": 1, "has_docstring": true, "content_length": 722, "token_count": 247, "document": "method _generate_generic_component in IntelligentAugmentEngine"}, "7b2136fc1beecb8b": {"name": "_validate_code_structure", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 452, "end_line": 526, "complexity": 25, "has_docstring": true, "content_length": 3338, "token_count": 1558, "document": "method _validate_code_structure in IntelligentAugmentEngine"}, "be8a445a288365ed": {"name": "_fix_structural_issues", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 528, "end_line": 552, "complexity": 7, "has_docstring": true, "content_length": 1179, "token_count": 495, "document": "method _fix_structural_issues in IntelligentAugmentEngine"}, "54393f5668ab3c22": {"name": "_execute_with_intelligent_handling", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 554, "end_line": 613, "complexity": 8, "has_docstring": true, "content_length": 2232, "token_count": 1265, "document": "method _execute_with_intelligent_handling in IntelligentAugmentEngine"}, "a5f458e45444b6d4": {"name": "_intelligent_error_fix", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 615, "end_line": 645, "complexity": 10, "has_docstring": true, "content_length": 1418, "token_count": 674, "document": "method _intelligent_error_fix in IntelligentAugmentEngine"}, "acf491c3e73d1577": {"name": "_add_timeout_handling", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 647, "end_line": 662, "complexity": 1, "has_docstring": true, "content_length": 422, "token_count": 188, "document": "method _add_timeout_handling in IntelligentAugmentEngine"}, "935100abe6049d20": {"name": "_create_failure_result", "type": "method", "parent": "IntelligentAugmentEngine", "start_line": 664, "end_line": 673, "complexity": 1, "has_docstring": true, "content_length": 337, "token_count": 204, "document": "method _create_failure_result in IntelligentAugmentEngine"}, "a4353e5ccd8c46ba": {"name": "Calculator", "type": "class", "parent": null, "start_line": 2, "end_line": 3, "complexity": 0, "has_docstring": true, "content_length": 103, "token_count": 47, "document": "class Calculator"}, "41e6a4f8647484df": {"name": "__init__", "type": "method", "parent": "Calculator", "start_line": 5, "end_line": 6, "complexity": 1, "has_docstring": false, "content_length": 36, "token_count": 31, "document": "method __init__ in Calculator"}, "6cecaec829a90ea5": {"name": "add", "type": "method", "parent": "Calculator", "start_line": 8, "end_line": 18, "complexity": 1, "has_docstring": true, "content_length": 293, "token_count": 211, "document": "method add in Calculator"}, "5b57e2c7c8eda194": {"name": "subtract", "type": "method", "parent": "Calculator", "start_line": 20, "end_line": 30, "complexity": 1, "has_docstring": true, "content_length": 325, "token_count": 221, "document": "method subtract in Calculator"}, "e978628130692e3d": {"name": "multiply", "type": "method", "parent": "Calculator", "start_line": 32, "end_line": 42, "complexity": 1, "has_docstring": true, "content_length": 318, "token_count": 215, "document": "method multiply in Calculator"}, "9ea46f95000dbb84": {"name": "divide", "type": "method", "parent": "Calculator", "start_line": 44, "end_line": 54, "complexity": 1, "has_docstring": true, "content_length": 305, "token_count": 217, "document": "method divide in Calculator"}, "8d78d389fa5db1a5": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "f06352f83002a9d0": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 8, "document": "import imports"}, "bc5593cbc2cc697c": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "693f0b1841a36792": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 15, "token_count": 9, "document": "import imports"}, "6c69b821fd71763e": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 8, "document": "import imports"}, "6f2314375beb2b50": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 16, "token_count": 10, "document": "import imports"}, "783f48c86f6f01fb": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "e771085da0836f09": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 8, "document": "import imports"}, "46f1078f4e08a535": {"name": "generate_random_data", "type": "function", "parent": null, "start_line": 11, "end_line": 12, "complexity": 1, "has_docstring": false, "content_length": 101, "token_count": 48, "document": "function generate_random_data"}, "8b1bc356227d6349": {"name": "bubble_sort", "type": "function", "parent": null, "start_line": 15, "end_line": 21, "complexity": 4, "has_docstring": false, "content_length": 204, "token_count": 130, "document": "function bubble_sort"}, "5d2a456545adfb7b": {"name": "quick_sort", "type": "function", "parent": null, "start_line": 24, "end_line": 30, "complexity": 2, "has_docstring": false, "content_length": 244, "token_count": 120, "document": "function quick_sort"}, "b8ba179ee076b6d5": {"name": "sort_lists", "type": "function", "parent": null, "start_line": 33, "end_line": 34, "complexity": 1, "has_docstring": false, "content_length": 60, "token_count": 31, "document": "function sort_lists"}, "f5f691b1cd0ff84b": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "9cd7077f2a6b4db0": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 8, "document": "import imports"}, "35ce6589b6694e6d": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "b83b8a3ac90c85db": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 9, "document": "import imports"}, "5fb26175c0448830": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "ee06d139cfd17666": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 8, "document": "import imports"}, "e8402d7d5e58c202": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "f3a4e3128b84d76b": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 15, "token_count": 9, "document": "import imports"}, "020f73a880bf1f54": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 8, "document": "import imports"}, "c840be4d68a835bb": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 16, "token_count": 10, "document": "import imports"}, "b6542ef57bb75834": {"name": "BinaryTree", "type": "class", "parent": null, "start_line": 12, "end_line": 12, "complexity": 0, "has_docstring": false, "content_length": 17, "token_count": 11, "document": "class BinaryTree"}, "e0e2aaa31975dc0d": {"name": "__init__", "type": "method", "parent": "BinaryTree", "start_line": 13, "end_line": 16, "complexity": 1, "has_docstring": false, "content_length": 108, "token_count": 64, "document": "method __init__ in BinaryTree"}, "625c55154af8b5a7": {"name": "insert", "type": "method", "parent": "BinaryTree", "start_line": 18, "end_line": 28, "complexity": 4, "has_docstring": false, "content_length": 352, "token_count": 210, "document": "method insert in BinaryTree"}, "8a360ab0fb517957": {"name": "search", "type": "method", "parent": "BinaryTree", "start_line": 30, "end_line": 38, "complexity": 6, "has_docstring": false, "content_length": 329, "token_count": 162, "document": "method search in BinaryTree"}, "8a24c6eac6c7142f": {"name": "__str__", "type": "method", "parent": "BinaryTree", "start_line": 40, "end_line": 49, "complexity": 3, "has_docstring": false, "content_length": 371, "token_count": 201, "document": "method __str__ in BinaryTree"}, "89235bc67d8e386b": {"name": "generate_data", "type": "function", "parent": null, "start_line": 51, "end_line": 55, "complexity": 2, "has_docstring": false, "content_length": 117, "token_count": 58, "document": "function generate_data"}, "bc5d8cb5d3808997": {"name": "main", "type": "function", "parent": null, "start_line": 57, "end_line": 69, "complexity": 4, "has_docstring": false, "content_length": 340, "token_count": 162, "document": "function main"}, "42e9d4a8e3e87000": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 18, "token_count": 8, "document": "import imports"}, "b5eee59f9f0cf31c": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 15, "token_count": 9, "document": "import imports"}, "52f94dd82e459eee": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 16, "token_count": 10, "document": "import imports"}, "03bc10e379e5eafa": {"name": "imports", "type": "import", "parent": null, "start_line": 4, "end_line": 4, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "5eec2e1b3e9c92ff": {"name": "imports", "type": "import", "parent": null, "start_line": 5, "end_line": 5, "complexity": 0, "has_docstring": false, "content_length": 9, "token_count": 8, "document": "import imports"}, "8e8ea7da352c48ad": {"name": "imports", "type": "import", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 8, "document": "import imports"}, "5795da4e59696fc9": {"name": "imports", "type": "import", "parent": null, "start_line": 7, "end_line": 7, "complexity": 0, "has_docstring": false, "content_length": 10, "token_count": 8, "document": "import imports"}, "0fd596276ec02163": {"name": "imports", "type": "import", "parent": null, "start_line": 8, "end_line": 8, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "f788fb7cab3c0eac": {"name": "imports", "type": "import", "parent": null, "start_line": 9, "end_line": 9, "complexity": 0, "has_docstring": false, "content_length": 35, "token_count": 11, "document": "import imports"}, "d268efb9fafd9cb1": {"name": "imports", "type": "import", "parent": null, "start_line": 10, "end_line": 10, "complexity": 0, "has_docstring": false, "content_length": 29, "token_count": 12, "document": "import imports"}, "75f6484767c5fa9f": {"name": "imports", "type": "import", "parent": null, "start_line": 11, "end_line": 11, "complexity": 0, "has_docstring": false, "content_length": 48, "token_count": 15, "document": "import imports"}, "ed02b2efe2b1cbd3": {"name": "imports", "type": "import", "parent": null, "start_line": 12, "end_line": 12, "complexity": 0, "has_docstring": false, "content_length": 26, "token_count": 13, "document": "import imports"}, "3bffb39f39387f6c": {"name": "HashTable", "type": "class", "parent": null, "start_line": 14, "end_line": 14, "complexity": 0, "has_docstring": false, "content_length": 16, "token_count": 11, "document": "class HashTable"}, "a4451746a8ba61f8": {"name": "__init__", "type": "method", "parent": "HashTable", "start_line": 15, "end_line": 16, "complexity": 1, "has_docstring": false, "content_length": 61, "token_count": 40, "document": "method __init__ in HashTable"}, "7a49fab1b5e00c0a": {"name": "put", "type": "method", "parent": "HashTable", "start_line": 18, "end_line": 19, "complexity": 1, "has_docstring": false, "content_length": 67, "token_count": 41, "document": "method put in HashTable"}, "190e449137e48eb6": {"name": "get", "type": "method", "parent": "HashTable", "start_line": 21, "end_line": 22, "complexity": 1, "has_docstring": false, "content_length": 53, "token_count": 36, "document": "method get in HashTable"}, "f65d844684fd169d": {"name": "remove", "type": "method", "parent": "HashTable", "start_line": 24, "end_line": 26, "complexity": 2, "has_docstring": false, "content_length": 110, "token_count": 62, "document": "method remove in HashTable"}, "0aa04c7fc59eb531": {"name": "HashTableCollisionHandler", "type": "class", "parent": null, "start_line": 28, "end_line": 28, "complexity": 0, "has_docstring": false, "content_length": 32, "token_count": 17, "document": "class HashTableCollisionHandler"}, "402d670fe998627d": {"name": "__init__", "type": "method", "parent": "HashTableCollisionHandler", "start_line": 29, "end_line": 30, "complexity": 1, "has_docstring": false, "content_length": 61, "token_count": 43, "document": "method __init__ in HashTableCollisionHandler"}, "0985acb3b38ee648": {"name": "put", "type": "method", "parent": "HashTableCollisionHandler", "start_line": 32, "end_line": 33, "complexity": 1, "has_docstring": false, "content_length": 67, "token_count": 44, "document": "method put in HashTableCollisionHandler"}, "a84effcf13fb78e8": {"name": "get", "type": "method", "parent": "HashTableCollisionHandler", "start_line": 35, "end_line": 36, "complexity": 1, "has_docstring": false, "content_length": 53, "token_count": 39, "document": "method get in HashTableCollisionHandler"}, "df7f270403182878": {"name": "remove", "type": "method", "parent": "HashTableCollisionHandler", "start_line": 38, "end_line": 40, "complexity": 2, "has_docstring": false, "content_length": 110, "token_count": 65, "document": "method remove in HashTableCollisionHandler"}, "d03691a795c748c7": {"name": "main", "type": "function", "parent": null, "start_line": 42, "end_line": 49, "complexity": 1, "has_docstring": false, "content_length": 257, "token_count": 111, "document": "function main"}, "d20e21a2b68b85a0": {"name": "imports", "type": "import", "parent": null, "start_line": 1, "end_line": 1, "complexity": 0, "has_docstring": false, "content_length": 11, "token_count": 8, "document": "import imports"}, "87b23b2fde3ee8f1": {"name": "imports", "type": "import", "parent": null, "start_line": 2, "end_line": 2, "complexity": 0, "has_docstring": false, "content_length": 35, "token_count": 11, "document": "import imports"}, "217cc15e5a927072": {"name": "imports", "type": "import", "parent": null, "start_line": 3, "end_line": 3, "complexity": 0, "has_docstring": false, "content_length": 13, "token_count": 8, "document": "import imports"}, "c076a0a909177467": {"name": "SimpleCalculator", "type": "class", "parent": null, "start_line": 6, "end_line": 6, "complexity": 0, "has_docstring": false, "content_length": 23, "token_count": 15, "document": "class SimpleCalculator"}, "7bfdf8fc44e56199": {"name": "add", "type": "method", "parent": "SimpleCalculator", "start_line": 7, "end_line": 8, "complexity": 1, "has_docstring": false, "content_length": 45, "token_count": 37, "document": "method add in SimpleCalculator"}, "e4ba0f3e5ba497e4": {"name": "subtract", "type": "method", "parent": "SimpleCalculator", "start_line": 10, "end_line": 11, "complexity": 1, "has_docstring": false, "content_length": 50, "token_count": 37, "document": "method subtract in SimpleCalculator"}, "e7990ed037205b99": {"name": "multiply", "type": "method", "parent": "SimpleCalculator", "start_line": 13, "end_line": 14, "complexity": 1, "has_docstring": false, "content_length": 50, "token_count": 37, "document": "method multiply in SimpleCalculator"}, "34248e618d3d40df": {"name": "divide", "type": "method", "parent": "SimpleCalculator", "start_line": 16, "end_line": 19, "complexity": 2, "has_docstring": false, "content_length": 121, "token_count": 72, "document": "method divide in SimpleCalculator"}, "5fae15136c4703b4": {"name": "SimpleHashTable", "type": "class", "parent": null, "start_line": 22, "end_line": 22, "complexity": 0, "has_docstring": false, "content_length": 22, "token_count": 13, "document": "class SimpleHashTable"}, "3ec7d6c3b2e2b808": {"name": "__init__", "type": "method", "parent": "SimpleHashTable", "start_line": 23, "end_line": 24, "complexity": 1, "has_docstring": false, "content_length": 62, "token_count": 41, "document": "method __init__ in SimpleHashTable"}, "4fec6230acca10d6": {"name": "insert", "type": "method", "parent": "SimpleHashTable", "start_line": 26, "end_line": 27, "complexity": 1, "has_docstring": false, "content_length": 71, "token_count": 42, "document": "method insert in SimpleHashTable"}, "437c62ae2a9c99c2": {"name": "get", "type": "method", "parent": "SimpleHashTable", "start_line": 29, "end_line": 32, "complexity": 2, "has_docstring": false, "content_length": 108, "token_count": 66, "document": "method get in SimpleHashTable"}, "9616b9703e369fa5": {"name": "delete", "type": "method", "parent": "SimpleHashTable", "start_line": 34, "end_line": 36, "complexity": 2, "has_docstring": false, "content_length": 88, "token_count": 56, "document": "method delete in SimpleHashTable"}, "aebb70d33cf25527": {"name": "SortingAlgorithm", "type": "class", "parent": null, "start_line": 39, "end_line": 39, "complexity": 0, "has_docstring": false, "content_length": 23, "token_count": 15, "document": "class SortingAlgorithm"}, "e9761415514ba67e": {"name": "bubble_sort", "type": "method", "parent": "SortingAlgorithm", "start_line": 40, "end_line": 46, "complexity": 4, "has_docstring": false, "content_length": 230, "token_count": 160, "document": "method bubble_sort in SortingAlgorithm"}, "10ad8c1149708a21": {"name": "DataProcessingPipeline", "type": "class", "parent": null, "start_line": 49, "end_line": 49, "complexity": 0, "has_docstring": false, "content_length": 29, "token_count": 19, "document": "class DataProcessingPipeline"}, "eed54234d2a2d380": {"name": "__init__", "type": "method", "parent": "DataProcessingPipeline", "start_line": 50, "end_line": 53, "complexity": 1, "has_docstring": false, "content_length": 164, "token_count": 85, "document": "method __init__ in DataProcessingPipeline"}, "6339dc21826aee74": {"name": "process_data", "type": "method", "parent": "DataProcessingPipeline", "start_line": 55, "end_line": 65, "complexity": 1, "has_docstring": false, "content_length": 401, "token_count": 195, "document": "method process_data in DataProcessingPipeline"}, "d452e6fe478525e7": {"name": "generate_synthetic_data", "type": "function", "parent": null, "start_line": 68, "end_line": 69, "complexity": 1, "has_docstring": false, "content_length": 85, "token_count": 43, "document": "function generate_synthetic_data"}}