import csv
import matplotlib.pyplot as plt
import seaborn as sns

class CSVProcessor:
    def __init__(self, filename):
        self.filename = filename
        self.data = []

    def load_data(self):
        try:
            with open(self.filename, mode='r', newline='') as file:
                reader = csv.reader(file)
                for row in reader:
                    self.data.append(row)
            print(f"Data loaded from {self.filename}")
        except FileNotFoundError:
            print(f"File {self.filename} not found")
        except Exception as e:
            print(f"Error loading data: {e}")

    def process_data(self):
        if not self.data:
            print("No data to process")
            return

        # Example processing: convert all string numbers to integers
        processed_data = []
        for row in self.data[1:]:  # Skip header
            try:
                processed_row = [int(item) for item in row]
                processed_data.append(processed_row)
            except ValueError:
                print("Error converting data to integer")

        return processed_data

# Example usage if this were main.py
if __name__ == "__main__":
    processor = CSVProcessor('example.csv')
    processor.load_data()
    processed_data = processor.process_data()

    # Visualize the data using seaborn
    sns.set(style="whitegrid")
    plt.figure(figsize=(10, 6))
    sns.boxplot(data=processed_data)
    plt.title("Processed Data Visualization")
    plt.xlabel("Columns")
    plt.ylabel("Values")
    plt.show()