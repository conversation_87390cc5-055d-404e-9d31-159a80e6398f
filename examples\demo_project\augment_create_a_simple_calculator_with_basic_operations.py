class SimpleCalculator:
    def add(self, a, b):
        """Add two numbers."""
        return a + b

    def subtract(self, a, b):
        """Subtract two numbers."""
        return a - b

    def multiply(self, a, b):
        """Multiply two numbers."""
        return a * b

    def divide(self, a, b):
        """Divide two numbers. Handle division by zero."""
        if b == 0:
            raise ValueError("Cannot divide by zero.")
        return a / b

def main():
    calculator = SimpleCalculator()
    
    # Test data
    test_cases = [
        (5, 3, '+'),
        (10, 4, '-'),
        (6, 2, '*'),
        (8, 2, '/'),
        (7, 0, '/')
    ]
    
    for a, b, op in test_cases:
        try:
            if op == '+':
                result = calculator.add(a, b)
            elif op == '-':
                result = calculator.subtract(a, b)
            elif op == '*':
                result = calculator.multiply(a, b)
            elif op == '/':
                result = calculator.divide(a, b)
            else:
                raise ValueError(f"Unsupported operation: {op}")
            
            print(f"{a} {op} {b} = {result}")
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()