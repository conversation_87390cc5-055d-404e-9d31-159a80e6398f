import csv
import matplotlib.pyplot as plt
import seaborn as sns

class CSVProcessor:
    def __init__(self, filename):
        self.filename = filename
        self.data = []

    def load_data(self):
        with open(self.filename, mode='r', newline='') as file:
            reader = csv.reader(file)
            for row in reader:
                self.data.append(row)

    def get_data(self):
        return self.data

class DataAnalyzer:
    def __init__(self, processor):
        self.processor = processor
        self.data = []

    def analyze_data(self):
        try:
            self.processor.load_data()
            self.data = self.processor.get_data()
            if not self.data:
                raise ValueError("No data loaded from CSV file.")
            
            # Example analysis: Calculate mean of first column
            try:
                values = [float(row[0]) for row in self.data[1:]]
                mean_value = sum(values) / len(values)
                print(f"Mean value of the first column: {mean_value}")
                
                # Visualization
                plt.figure(figsize=(10, 6))
                sns.histplot(values, kde=True)
                plt.title('Histogram of First Column')
                plt.xlabel('Values')
                plt.ylabel('Frequency')
                plt.show()
            except ValueError as e:
                print(f"Error in data analysis: {e}")
        except Exception as e:
            print(f"An error occurred: {e}")

# Example usage
if __name__ == "__main__":
    processor = CSVProcessor("example.csv")
    analyzer = DataAnalyzer(processor)
    analyzer.analyze_data()