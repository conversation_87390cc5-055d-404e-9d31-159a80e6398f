import os
import sys
import time
import datetime
import collections
import itertools
import math
import random

# Define a function to generate random data for testing
def generate_random_data(n):
    return [[random.randint(0, 10) for _ in range(n)] for _ in range(n)]

# Define a function to perform bubble sort on a list of lists
def bubble_sort(arr):
    n = len(arr)
    for i in range(n-1):
        for j in range(0, n-i-1):
            if arr[j][0] > arr[j+1][0]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

# Define a function to perform quick sort on a list of lists
def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[0][0]
    less = [x for x in arr[1:] if x[0] <= pivot]
    greater = [x for x in arr[1:] if x[0] > pivot]
    return quick_sort(less) + [arr[0]] + quick_sort(greater)

# Define a function to perform the sorting algorithm on a list of lists
def sort_lists(arr):
    return quick_sort(bubble_sort(arr))

# Test the sorting algorithm with random data
if __name__ == "__main__":
    n = 10
    arr = generate_random_data(n)
    sorted_arr = sort_lists(arr)
    print("Original array:", arr)
    print("Sorted array:", sorted_arr)