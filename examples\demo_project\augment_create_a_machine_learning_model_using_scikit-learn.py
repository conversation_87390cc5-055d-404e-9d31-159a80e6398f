import sklearn.datasets
import sklearn.model_selection
from sklearn.linear_model import LogisticRegression

def train_model(X, y):
    """
    Trains a logistic regression model on the given data.
    
    Args:
        X (list of lists): Feature matrix.
        y (list): Target vector.
        
    Returns:
        LogisticRegression: Trained model.
    """
    model = LogisticRegression(max_iter=200)
    model.fit(X, y)
    return model

def predict(model, X):
    """
    Predicts the target values for new data using the trained model.
    
    Args:
        model (LogisticRegression): Trained model.
        X (list of lists): Feature matrix for prediction.
        
    Returns:
        list: Predicted target values.
    """
    return model.predict(X)

def main():
    # Load a sample dataset
    iris = sklearn.datasets.load_iris()
    X = iris.data
    y = iris.target
    
    # Split the data into training and testing sets
    X_train, X_test, y_train, y_test = sklearn.model_selection.train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Train the model
    model = train_model(X_train, y_train)
    
    # Make predictions
    predictions = predict(model, X_test)
    
    # Print the results
    print("Predictions:   ", predictions)
    print("Actual targets:", y_test)

if __name__ == "__main__":
    main()