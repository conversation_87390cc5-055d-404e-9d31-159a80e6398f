import csv
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns

class CSVProcessor:
    def __init__(self, filename):
        self.filename = filename

    def load_data(self):
        data = []
        with open(self.filename, mode='r') as file:
            reader = csv.reader(file)
            next(reader)  # Skip the header row
            for row in reader:
                data.append(row)
        return data

class DataAnalyzer:
    def __init__(self, data):
        self.data = data

    def generate_summary(self):
        summary = {}
        for row in self.data:
            if row[0] not in summary:
                summary[row[0]] = 0
            summary[row[0]] += int(row[1])
        return summary

class Visualizer:
    def __init__(self, data):
        self.data = data

    def plot_data(self):
        labels = list(self.data.keys())
        values = list(self.data.values())

        plt.figure(figsize=(10, 5))
        sns.barplot(x=labels, y=values)
        plt.title('Data Summary')
        plt.xlabel('Category')
        plt.ylabel('Value')
        plt.savefig('data_analysis_plot.png')
        print('Plot saved as data_analysis_plot.png')

if __name__ == "__main__":
    # Example usage
    processor = CSVProcessor('examples/demo_project/data.csv')
    data = processor.load_data()

    analyzer = DataAnalyzer(data)
    summary = analyzer.generate_summary()
    print('Data Summary:', summary)

    visualizer = Visualizer(summary)
    visualizer.plot_data()
    print('Data analysis completed successfully!')
