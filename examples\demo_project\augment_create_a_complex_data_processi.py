import json
from collections import defaultdict
import random

# Define a simple calculator class
class SimpleCalculator:
    def add(self, a, b):
        return a + b
    
    def subtract(self, a, b):
        return a - b
    
    def multiply(self, a, b):
        return a * b
    
    def divide(self, a, b):
        if b == 0:
            raise ValueError("Cannot divide by zero")
        return a / b

# Define a simple hash table class
class SimpleHashTable:
    def __init__(self):
        self.table = defaultdict(list)
    
    def insert(self, key, value):
        self.table[key].append(value)
    
    def get(self, key):
        if key in self.table:
            return self.table[key]
        return None
    
    def delete(self, key):
        if key in self.table:
            del self.table[key]

# Define a sorting algorithm class
class SortingAlgorithm:
    def bubble_sort(self, arr):
        n = len(arr)
        for i in range(n):
            for j in range(0, n-i-1):
                if arr[j] > arr[j+1]:
                    arr[j], arr[j+1] = arr[j+1], arr[j]
        return arr

# Define a data processing pipeline class
class DataProcessingPipeline:
    def __init__(self):
        self.calculator = SimpleCalculator()
        self.hash_table = SimpleHashTable()
        self.sorting_algorithm = SortingAlgorithm()
    
    def process_data(self, data):
        # Stage 1: Calculate the sum of all numbers
        total_sum = sum(data)
        
        # Stage 2: Insert the sum into a hash table
        self.hash_table.insert('total_sum', total_sum)
        
        # Stage 3: Sort the data using bubble sort
        sorted_data = self.sorting_algorithm.bubble_sort(data)
        
        return sorted_data, total_sum

# Define a function to generate synthetic data
def generate_synthetic_data():
    return [random.randint(1, 100) for _ in range(10)]

# Main function to execute the pipeline
if __name__ == "__main__":
    # Generate synthetic data
    data = generate_synthetic_data()
    
    # Create a data processing pipeline instance
    pipeline = DataProcessingPipeline()
    
    try:
        # Process the data
        sorted_data, total_sum = pipeline.process_data(data)
        
        # Output the results
        print("Original Data:", data)
        print("Sorted Data:", sorted_data)
        print("Total Sum:", total_sum)
        
        # Retrieve and output the sum from the hash table
        stored_sum = pipeline.hash_table.get('total_sum')
        print("Stored Total Sum:", stored_sum)
    
    except Exception as e:
        print(f"An error occurred: {e}")