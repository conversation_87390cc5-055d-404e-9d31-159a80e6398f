import collections
import datetime
import itertools
import math
import os
import random
import sys
import time
from collections import defaultdict
from datetime import datetime
from itertools import combinations, permutations
from math import exp, tanh

class HashTable:
    def __init__(self):
        self.data = defaultdict(list)

    def put(self, key, value):
        self.data[key].append(value)

    def get(self, key):
        return self.data[key]

    def remove(self, key, value):
        if value in self.data[key]:
            self.data[key].remove(value)

class HashTableCollisionHandler:
    def __init__(self):
        self.data = defaultdict(list)

    def put(self, key, value):
        self.data[key].append(value)

    def get(self, key):
        return self.data[key]

    def remove(self, key, value):
        if value in self.data[key]:
            self.data[key].remove(value)

def main():
    hash_table = HashTable()
    hash_table.put("apple", "red")
    hash_table.put("banana", "yellow")
    hash_table.put("cherry", "red")
    print(hash_table.get("apple"))
    print(hash_table.get("banana"))
    print(hash_table.get("cherry"))

if __name__ == "__main__":
    main()