import requests
from bs4 import BeautifulSoup
import json

class WebScraper:
    def __init__(self, url):
        self.url = url

    def fetch_data(self):
        try:
            response = requests.get(self.url)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            print(f"Error fetching data: {e}")
            return None

    def parse_content(self, html):
        if not html:
            return []
        soup = BeautifulSoup(html, 'html.parser')
        titles = [title.get_text() for title in soup.find_all('h1')]
        return titles

def save_data(data, filename='output.json'):
    try:
        with open(filename, 'w') as f:
            json.dump(data, f, indent=4)
        print(f"Data saved to {filename}")
    except IOError as e:
        print(f"Error saving data: {e}")

def main():
    url = 'https://en.wikipedia.org/wiki/Banu_Mushtaq'
    scraper = WebScraper(url)
    html = scraper.fetch_data()
    if html:
        titles = scraper.parse_content(html)
        save_data(titles)

if __name__ == "__main__":
    main()