import math
import random
import json
import csv
import os
import sys
import time
import datetime
import collections
import itertools

class BinaryTree:
    def __init__(self, value):
        self.value = value
        self.left = None
        self.right = None
    
    def insert(self, value):
        if value < self.value:
            if self.left is None:
                self.left = BinaryTree(value)
            else:
                self.left.insert(value)
        else:
            if self.right is None:
                self.right = BinaryTree(value)
            else:
                self.right.insert(value)
    
    def search(self, value):
        if value == self.value:
            return True
        elif value < self.value and self.left is not None:
            return self.left.search(value)
        elif value > self.value and self.right is not None:
            return self.right.search(value)
        else:
            return False
    
    def __str__(self):
        if self.left is not None:
            left_string = str(self.left)
        else:
            left_string = ''
        if self.right is not None:
            right_string = str(self.right)
        else:
            right_string = ''
        return 'Value: ' + str(self.value) + '\n' + 'Left: ' + left_string + '\n' + 'Right: ' + right_string

def generate_data():
    data = []
    for i in range(10):
        data.append(random.randint(1, 10))
    return data

def main():
    tree = BinaryTree(5)
    data = generate_data()
    for value in data:
        tree.insert(value)
    print('Original Tree:')
    print(tree)
    print('Searching for values...')
    for value in data:
        if tree.search(value):
            print(f'Found {value}!')
        else:
            print(f'{value} not found.')

if __name__ == '__main__':
    main()